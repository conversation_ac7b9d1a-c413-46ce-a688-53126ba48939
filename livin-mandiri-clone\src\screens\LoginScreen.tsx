import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Image,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Button, Input, Card, Loading } from '../components';
import { Colors, Typography, Spacing, BorderRadius, Layout } from '../constants';

interface LoginScreenProps {
  navigation: any; // Replace with proper navigation type
}

export const LoginScreen: React.FC<LoginScreenProps> = ({ navigation }) => {
  const [userId, setUserId] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{ userId?: string; password?: string }>({});
  
  const insets = useSafeAreaInsets();

  const validateForm = () => {
    const newErrors: { userId?: string; password?: string } = {};
    
    if (!userId.trim()) {
      newErrors.userId = 'User ID is required';
    } else if (userId.length < 3) {
      newErrors.userId = 'User ID must be at least 3 characters';
    }
    
    if (!password.trim()) {
      newErrors.password = 'Password is required';
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;
    
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      // Navigate to main app
      navigation.replace('MainTabs');
    }, 2000);
  };

  const handleBiometricLogin = () => {
    // Implement biometric authentication
    console.log('Biometric login');
  };

  const handleForgotPassword = () => {
    // Navigate to forgot password screen
    console.log('Forgot password');
  };

  const handleRegister = () => {
    // Navigate to registration screen
    console.log('Register');
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={Colors.gradient.primary}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      >
        <KeyboardAvoidingView
          style={styles.keyboardAvoid}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView
            contentContainerStyle={[
              styles.scrollContent,
              { paddingTop: insets.top + 20 }
            ]}
            showsVerticalScrollIndicator={false}
          >
            {/* Logo Section */}
            <View style={styles.logoSection}>
              <View style={styles.logoContainer}>
                <Ionicons
                  name="card"
                  size={60}
                  color={Colors.text.inverse}
                />
              </View>
              <Text style={styles.logoText}>Livin' by Mandiri</Text>
              <Text style={styles.tagline}>Your Digital Banking Partner</Text>
            </View>

            {/* Login Form */}
            <Card
              variant="default"
              padding="lg"
              margin="md"
              style={styles.loginCard}
            >
              <Text style={styles.welcomeText}>Welcome Back!</Text>
              <Text style={styles.subtitleText}>
                Sign in to access your account
              </Text>

              <View style={styles.formContainer}>
                <Input
                  label="User ID"
                  placeholder="Enter your User ID"
                  value={userId}
                  onChangeText={setUserId}
                  error={errors.userId}
                  leftIcon="person"
                  autoCapitalize="none"
                  autoCorrect={false}
                  required
                />

                <Input
                  label="Password"
                  placeholder="Enter your password"
                  value={password}
                  onChangeText={setPassword}
                  error={errors.password}
                  leftIcon="lock-closed"
                  secureTextEntry
                  required
                />

                <TouchableOpacity
                  onPress={handleForgotPassword}
                  style={styles.forgotPassword}
                >
                  <Text style={styles.forgotPasswordText}>
                    Forgot Password?
                  </Text>
                </TouchableOpacity>

                <Button
                  title="Sign In"
                  onPress={handleLogin}
                  variant="gradient"
                  size="large"
                  fullWidth
                  loading={isLoading}
                  style={styles.loginButton}
                />

                {/* Biometric Login */}
                <View style={styles.biometricSection}>
                  <View style={styles.divider}>
                    <View style={styles.dividerLine} />
                    <Text style={styles.dividerText}>or</Text>
                    <View style={styles.dividerLine} />
                  </View>

                  <TouchableOpacity
                    onPress={handleBiometricLogin}
                    style={styles.biometricButton}
                  >
                    <Ionicons
                      name="finger-print"
                      size={Layout.iconSize.lg}
                      color={Colors.primary.main}
                    />
                    <Text style={styles.biometricText}>
                      Use Fingerprint
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </Card>

            {/* Register Section */}
            <View style={styles.registerSection}>
              <Text style={styles.registerPrompt}>
                Don't have an account?
              </Text>
              <TouchableOpacity onPress={handleRegister}>
                <Text style={styles.registerLink}>
                  Register Now
                </Text>
              </TouchableOpacity>
            </View>

            {/* Footer */}
            <View style={styles.footer}>
              <Text style={styles.footerText}>
                © 2024 Bank Mandiri. All rights reserved.
              </Text>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </LinearGradient>

      <Loading visible={isLoading} text="Signing you in..." />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  gradient: {
    flex: 1,
  },

  keyboardAvoid: {
    flex: 1,
  },

  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: Spacing.md,
    paddingBottom: Spacing.xl,
  },

  logoSection: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
    paddingVertical: Spacing.xl,
  },

  logoContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.md,
  },

  logoText: {
    ...Typography.h2,
    color: Colors.text.inverse,
    fontWeight: 'bold',
    marginBottom: Spacing.xs,
  },

  tagline: {
    ...Typography.body,
    color: Colors.text.inverse,
    opacity: 0.9,
  },

  loginCard: {
    marginHorizontal: 0,
  },

  welcomeText: {
    ...Typography.h3,
    color: Colors.text.primary,
    textAlign: 'center',
    marginBottom: Spacing.xs,
  },

  subtitleText: {
    ...Typography.body,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },

  formContainer: {
    marginTop: Spacing.md,
  },

  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: Spacing.lg,
  },

  forgotPasswordText: {
    ...Typography.bodySmall,
    color: Colors.primary.main,
    fontWeight: '500',
  },

  loginButton: {
    marginBottom: Spacing.lg,
  },

  biometricSection: {
    alignItems: 'center',
  },

  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
    width: '100%',
  },

  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: Colors.border.light,
  },

  dividerText: {
    ...Typography.caption,
    color: Colors.text.tertiary,
    marginHorizontal: Spacing.md,
  },

  biometricButton: {
    alignItems: 'center',
    padding: Spacing.md,
  },

  biometricText: {
    ...Typography.bodySmall,
    color: Colors.primary.main,
    marginTop: Spacing.xs,
    fontWeight: '500',
  },

  registerSection: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: Spacing.xl,
    gap: Spacing.xs,
  },

  registerPrompt: {
    ...Typography.body,
    color: Colors.text.inverse,
  },

  registerLink: {
    ...Typography.body,
    color: Colors.text.inverse,
    fontWeight: 'bold',
    textDecorationLine: 'underline',
  },

  footer: {
    marginTop: Spacing.xl,
    alignItems: 'center',
  },

  footerText: {
    ...Typography.caption,
    color: Colors.text.inverse,
    opacity: 0.7,
    textAlign: 'center',
  },
});
