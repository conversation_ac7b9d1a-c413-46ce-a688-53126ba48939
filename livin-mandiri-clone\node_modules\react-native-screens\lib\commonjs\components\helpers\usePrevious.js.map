{"version": 3, "names": ["_react", "require", "usePrevious", "state", "ref", "useRef", "useEffect", "current"], "sourceRoot": "../../../../src", "sources": ["components/helpers/usePrevious.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAEO,SAASC,WAAWA,CAAIC,KAAQ,EAAiB;EACtD,MAAMC,GAAG,GAAG,IAAAC,aAAM,EAAI,CAAC;EAEvB,IAAAC,gBAAS,EAAC,MAAM;IACdF,GAAG,CAACG,OAAO,GAAGJ,KAAK;EACrB,CAAC,CAAC;EAEF,OAAOC,GAAG,CAACG,OAAO;AACpB", "ignoreList": []}