{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_SplitViewHostNativeComponent", "e", "__esModule", "default", "_extends", "Object", "assign", "bind", "n", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "displayModeForSplitViewCompatibilityMap", "tile", "overlay", "displace", "automatic", "isValidDisplayModeForSplitBehavior", "displayMode", "splitBehavior", "includes", "SplitViewHost", "props", "React", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "validDisplayModes", "console", "warn", "join", "createElement", "style", "styles", "container", "children", "StyleSheet", "create", "flex", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/gamma/SplitViewHost.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,6BAAA,GAAAH,sBAAA,CAAAC,OAAA;AAA2F,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,SAAA,WAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAP,CAAA,MAAAA,CAAA,GAAAQ,SAAA,CAAAC,MAAA,EAAAT,CAAA,UAAAU,CAAA,GAAAF,SAAA,CAAAR,CAAA,YAAAW,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAJ,CAAA,CAAAI,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAJ,CAAA,KAAAJ,QAAA,CAAAW,KAAA,OAAAN,SAAA;AAe3F;AACA;AACA;AACA;AACA,MAAMO,uCAGL,GAAG;EACFC,IAAI,EAAE,CAAC,eAAe,EAAE,oBAAoB,EAAE,oBAAoB,CAAC;EACnEC,OAAO,EAAE,CAAC,eAAe,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;EAClEC,QAAQ,EAAE,CAAC,eAAe,EAAE,oBAAoB,EAAE,sBAAsB,CAAC;EACzEC,SAAS,EAAE,EAAE,CAAE;AACjB,CAAC;AAED,MAAMC,kCAAkC,GAAGA,CACzCC,WAAiC,EACjCC,aAAqC,KAClC;EACH,IAAIA,aAAa,KAAK,WAAW,EAAE;IACjC;IACA,OAAO,IAAI;EACb;EACA,OAAOP,uCAAuC,CAACO,aAAa,CAAC,CAACC,QAAQ,CACpEF,WACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA,SAASG,aAAaA,CAACC,KAAyB,EAAE;EAChD,MAAM;IAAEJ,WAAW;IAAEC;EAAc,CAAC,GAAGG,KAAK;EAE5CC,cAAK,CAACC,SAAS,CAAC,MAAM;IACpB,IAAIN,WAAW,IAAIC,aAAa,EAAE;MAChC,MAAMM,OAAO,GAAGR,kCAAkC,CAChDC,WAAW,EACXC,aACF,CAAC;MACD,IAAI,CAACM,OAAO,EAAE;QACZ,MAAMC,iBAAiB,GACrBd,uCAAuC,CAACO,aAAa,CAAC;QACxDQ,OAAO,CAACC,IAAI,CACV,yBAAyBV,WAAW,yBAAyBC,aAAa,IAAI,GAC5E,sBAAsBA,aAAa,UAAUO,iBAAiB,CAACG,IAAI,CACjE,IACF,CAAC,GACL,CAAC;MACH;IACF;EACF,CAAC,EAAE,CAACX,WAAW,EAAEC,aAAa,CAAC,CAAC;EAEhC,oBACE3B,MAAA,CAAAO,OAAA,CAAA+B,aAAA,CAAClC,6BAAA,CAAAG,OAA4B,EAAAC,QAAA,KAAKsB,KAAK;IAAES,KAAK,EAAEC,MAAM,CAACC;EAAU,IAC9DX,KAAK,CAACY,QACqB,CAAC;AAEnC;AAEA,MAAMF,MAAM,GAAGG,uBAAU,CAACC,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAxC,OAAA,GAEYsB,aAAa", "ignoreList": []}