// Livin' by Mandiri Typography System
// Based on research: Avenir (clean, modern) and Courier New (classic charm)

import { Platform } from 'react-native';

// Font families based on platform
export const FontFamily = {
  // Primary font - Avenir (clean and modern)
  primary: Platform.select({
    ios: 'Avenir',
    android: 'sans-serif',
    default: 'System',
  }),
  
  // Secondary font - Courier New (classic charm)
  secondary: Platform.select({
    ios: 'Courier New',
    android: 'monospace',
    default: 'monospace',
  }),
  
  // System fonts as fallback
  system: Platform.select({
    ios: 'System',
    android: 'Roboto',
    default: 'System',
  }),
} as const;

// Font weights
export const FontWeight = {
  light: '300' as const,
  regular: '400' as const,
  medium: '500' as const,
  semiBold: '600' as const,
  bold: '700' as const,
  extraBold: '800' as const,
};

// Font sizes following a consistent scale
export const FontSize = {
  xs: 12,
  sm: 14,
  base: 16,
  lg: 18,
  xl: 20,
  '2xl': 24,
  '3xl': 28,
  '4xl': 32,
  '5xl': 36,
  '6xl': 48,
} as const;

// Line heights
export const LineHeight = {
  tight: 1.2,
  normal: 1.4,
  relaxed: 1.6,
  loose: 1.8,
} as const;

// Typography styles for different text elements
export const Typography = {
  // Headers
  h1: {
    fontFamily: FontFamily.primary,
    fontSize: FontSize['4xl'],
    fontWeight: FontWeight.bold,
    lineHeight: FontSize['4xl'] * LineHeight.tight,
  },
  
  h2: {
    fontFamily: FontFamily.primary,
    fontSize: FontSize['3xl'],
    fontWeight: FontWeight.bold,
    lineHeight: FontSize['3xl'] * LineHeight.tight,
  },
  
  h3: {
    fontFamily: FontFamily.primary,
    fontSize: FontSize['2xl'],
    fontWeight: FontWeight.semiBold,
    lineHeight: FontSize['2xl'] * LineHeight.normal,
  },
  
  h4: {
    fontFamily: FontFamily.primary,
    fontSize: FontSize.xl,
    fontWeight: FontWeight.semiBold,
    lineHeight: FontSize.xl * LineHeight.normal,
  },
  
  h5: {
    fontFamily: FontFamily.primary,
    fontSize: FontSize.lg,
    fontWeight: FontWeight.medium,
    lineHeight: FontSize.lg * LineHeight.normal,
  },
  
  h6: {
    fontFamily: FontFamily.primary,
    fontSize: FontSize.base,
    fontWeight: FontWeight.medium,
    lineHeight: FontSize.base * LineHeight.normal,
  },
  
  // Body text
  bodyLarge: {
    fontFamily: FontFamily.primary,
    fontSize: FontSize.lg,
    fontWeight: FontWeight.regular,
    lineHeight: FontSize.lg * LineHeight.relaxed,
  },
  
  body: {
    fontFamily: FontFamily.primary,
    fontSize: FontSize.base,
    fontWeight: FontWeight.regular,
    lineHeight: FontSize.base * LineHeight.normal,
  },
  
  bodySmall: {
    fontFamily: FontFamily.primary,
    fontSize: FontSize.sm,
    fontWeight: FontWeight.regular,
    lineHeight: FontSize.sm * LineHeight.normal,
  },
  
  // Special text styles
  caption: {
    fontFamily: FontFamily.primary,
    fontSize: FontSize.xs,
    fontWeight: FontWeight.regular,
    lineHeight: FontSize.xs * LineHeight.normal,
  },
  
  overline: {
    fontFamily: FontFamily.primary,
    fontSize: FontSize.xs,
    fontWeight: FontWeight.medium,
    lineHeight: FontSize.xs * LineHeight.normal,
    textTransform: 'uppercase' as const,
    letterSpacing: 1.5,
  },
  
  // Button text
  button: {
    fontFamily: FontFamily.primary,
    fontSize: FontSize.base,
    fontWeight: FontWeight.semiBold,
    lineHeight: FontSize.base * LineHeight.tight,
  },
  
  buttonSmall: {
    fontFamily: FontFamily.primary,
    fontSize: FontSize.sm,
    fontWeight: FontWeight.semiBold,
    lineHeight: FontSize.sm * LineHeight.tight,
  },
  
  buttonLarge: {
    fontFamily: FontFamily.primary,
    fontSize: FontSize.lg,
    fontWeight: FontWeight.semiBold,
    lineHeight: FontSize.lg * LineHeight.tight,
  },
  
  // Monospace text (for account numbers, amounts, etc.)
  monospace: {
    fontFamily: FontFamily.secondary,
    fontSize: FontSize.base,
    fontWeight: FontWeight.regular,
    lineHeight: FontSize.base * LineHeight.normal,
  },
  
  monospaceSmall: {
    fontFamily: FontFamily.secondary,
    fontSize: FontSize.sm,
    fontWeight: FontWeight.regular,
    lineHeight: FontSize.sm * LineHeight.normal,
  },
  
  monospaceLarge: {
    fontFamily: FontFamily.secondary,
    fontSize: FontSize.lg,
    fontWeight: FontWeight.regular,
    lineHeight: FontSize.lg * LineHeight.normal,
  },
} as const;

// Text style utility function
export const createTextStyle = (
  size: keyof typeof FontSize,
  weight: keyof typeof FontWeight,
  family: keyof typeof FontFamily = 'primary'
) => ({
  fontFamily: FontFamily[family],
  fontSize: FontSize[size],
  fontWeight: FontWeight[weight],
  lineHeight: FontSize[size] * LineHeight.normal,
});
