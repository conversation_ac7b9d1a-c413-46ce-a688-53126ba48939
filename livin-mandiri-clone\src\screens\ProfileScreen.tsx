import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Loading } from '../components';
import { Colors, Typography, Spacing, BorderRadius, Layout } from '../constants';

interface ProfileScreenProps {
  navigation: any;
}

interface MenuItem {
  id: string;
  title: string;
  subtitle?: string;
  icon: keyof typeof Ionicons.glyphMap;
  onPress: () => void;
  showArrow?: boolean;
  rightComponent?: React.ReactNode;
}

export const ProfileScreen: React.FC<ProfileScreenProps> = ({ navigation }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [biometricEnabled, setBiometricEnabled] = useState(true);

  const userInfo = {
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+62 812 3456 7890',
    memberSince: '2020',
    accountType: 'Premium',
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => {
            setIsLoading(true);
            setTimeout(() => {
              setIsLoading(false);
              navigation.replace('Login');
            }, 1000);
          },
        },
      ]
    );
  };

  const accountMenuItems: MenuItem[] = [
    {
      id: 'personal-info',
      title: 'Personal Information',
      subtitle: 'Update your personal details',
      icon: 'person',
      onPress: () => console.log('Personal Info'),
    },
    {
      id: 'account-settings',
      title: 'Account Settings',
      subtitle: 'Manage your account preferences',
      icon: 'settings',
      onPress: () => console.log('Account Settings'),
    },
    {
      id: 'linked-accounts',
      title: 'Linked Accounts',
      subtitle: 'Manage connected accounts',
      icon: 'link',
      onPress: () => console.log('Linked Accounts'),
    },
  ];

  const securityMenuItems: MenuItem[] = [
    {
      id: 'change-pin',
      title: 'Change PIN',
      subtitle: 'Update your security PIN',
      icon: 'lock-closed',
      onPress: () => console.log('Change PIN'),
    },
    {
      id: 'biometric',
      title: 'Biometric Login',
      subtitle: 'Use fingerprint or face recognition',
      icon: 'finger-print',
      onPress: () => setBiometricEnabled(!biometricEnabled),
      rightComponent: (
        <Switch
          value={biometricEnabled}
          onValueChange={setBiometricEnabled}
          trackColor={{
            false: Colors.neutral.gray300,
            true: Colors.primary.light,
          }}
          thumbColor={biometricEnabled ? Colors.primary.main : Colors.neutral.gray400}
        />
      ),
      showArrow: false,
    },
    {
      id: 'notifications',
      title: 'Notifications',
      subtitle: 'Manage notification preferences',
      icon: 'notifications',
      onPress: () => setNotificationsEnabled(!notificationsEnabled),
      rightComponent: (
        <Switch
          value={notificationsEnabled}
          onValueChange={setNotificationsEnabled}
          trackColor={{
            false: Colors.neutral.gray300,
            true: Colors.primary.light,
          }}
          thumbColor={notificationsEnabled ? Colors.primary.main : Colors.neutral.gray400}
        />
      ),
      showArrow: false,
    },
  ];

  const supportMenuItems: MenuItem[] = [
    {
      id: 'help-center',
      title: 'Help Center',
      subtitle: 'Get help and support',
      icon: 'help-circle',
      onPress: () => console.log('Help Center'),
    },
    {
      id: 'contact-us',
      title: 'Contact Us',
      subtitle: 'Reach out to customer service',
      icon: 'call',
      onPress: () => console.log('Contact Us'),
    },
    {
      id: 'about',
      title: 'About',
      subtitle: 'App version and information',
      icon: 'information-circle',
      onPress: () => console.log('About'),
    },
  ];

  const renderProfileHeader = () => (
    <Card
      variant="gradient"
      gradientColors={Colors.gradient.primary}
      padding="lg"
      style={styles.profileCard}
    >
      <View style={styles.profileHeader}>
        <View style={styles.avatarContainer}>
          <Text style={styles.avatarText}>
            {userInfo.name.split(' ').map(n => n[0]).join('')}
          </Text>
        </View>
        <View style={styles.profileInfo}>
          <Text style={styles.userName}>{userInfo.name}</Text>
          <Text style={styles.userEmail}>{userInfo.email}</Text>
          <Text style={styles.userPhone}>{userInfo.phone}</Text>
        </View>
        <TouchableOpacity style={styles.editButton}>
          <Ionicons
            name="pencil"
            size={Layout.iconSize.sm}
            color={Colors.text.inverse}
          />
        </TouchableOpacity>
      </View>

      <View style={styles.membershipInfo}>
        <View style={styles.membershipItem}>
          <Text style={styles.membershipLabel}>Member Since</Text>
          <Text style={styles.membershipValue}>{userInfo.memberSince}</Text>
        </View>
        <View style={styles.membershipDivider} />
        <View style={styles.membershipItem}>
          <Text style={styles.membershipLabel}>Account Type</Text>
          <Text style={styles.membershipValue}>{userInfo.accountType}</Text>
        </View>
      </View>
    </Card>
  );

  const renderMenuSection = (title: string, items: MenuItem[]) => (
    <Card padding="lg" style={styles.menuCard}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {items.map((item, index) => (
        <TouchableOpacity
          key={item.id}
          style={[
            styles.menuItem,
            index === items.length - 1 && styles.lastMenuItem,
          ]}
          onPress={item.onPress}
        >
          <View style={styles.menuItemLeft}>
            <View style={styles.menuIcon}>
              <Ionicons
                name={item.icon}
                size={Layout.iconSize.md}
                color={Colors.primary.main}
              />
            </View>
            <View style={styles.menuContent}>
              <Text style={styles.menuTitle}>{item.title}</Text>
              {item.subtitle && (
                <Text style={styles.menuSubtitle}>{item.subtitle}</Text>
              )}
            </View>
          </View>
          <View style={styles.menuItemRight}>
            {item.rightComponent}
            {item.showArrow !== false && (
              <Ionicons
                name="chevron-forward"
                size={Layout.iconSize.sm}
                color={Colors.neutral.gray400}
                style={item.rightComponent && styles.arrowWithComponent}
              />
            )}
          </View>
        </TouchableOpacity>
      ))}
    </Card>
  );

  return (
    <View style={styles.container}>
      <Header
        title="Profile"
        rightIcon="qr-code"
        onRightPress={() => console.log('QR Code')}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {renderProfileHeader()}
        {renderMenuSection('Account', accountMenuItems)}
        {renderMenuSection('Security', securityMenuItems)}
        {renderMenuSection('Support', supportMenuItems)}

        <View style={styles.logoutContainer}>
          <Button
            title="Logout"
            onPress={handleLogout}
            variant="outline"
            size="large"
            fullWidth
            style={styles.logoutButton}
          />
        </View>
      </ScrollView>

      <Loading visible={isLoading} text="Logging out..." />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },

  scrollView: {
    flex: 1,
  },

  scrollContent: {
    paddingBottom: Spacing.xl,
  },

  profileCard: {
    marginHorizontal: Spacing.md,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },

  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },

  avatarContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },

  avatarText: {
    ...Typography.h4,
    color: Colors.text.inverse,
    fontWeight: 'bold',
  },

  profileInfo: {
    flex: 1,
  },

  userName: {
    ...Typography.h5,
    color: Colors.text.inverse,
    fontWeight: 'bold',
    marginBottom: 2,
  },

  userEmail: {
    ...Typography.bodySmall,
    color: Colors.text.inverse,
    opacity: 0.9,
    marginBottom: 2,
  },

  userPhone: {
    ...Typography.bodySmall,
    color: Colors.text.inverse,
    opacity: 0.9,
  },

  editButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  membershipInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.2)',
  },

  membershipItem: {
    flex: 1,
    alignItems: 'center',
  },

  membershipLabel: {
    ...Typography.caption,
    color: Colors.text.inverse,
    opacity: 0.8,
    marginBottom: 2,
  },

  membershipValue: {
    ...Typography.body,
    color: Colors.text.inverse,
    fontWeight: 'bold',
  },

  membershipDivider: {
    width: 1,
    height: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    marginHorizontal: Spacing.md,
  },

  menuCard: {
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.sm,
  },

  sectionTitle: {
    ...Typography.h6,
    color: Colors.text.primary,
    fontWeight: 'bold',
    marginBottom: Spacing.md,
  },

  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },

  lastMenuItem: {
    borderBottomWidth: 0,
  },

  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.neutral.gray50,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },

  menuContent: {
    flex: 1,
  },

  menuTitle: {
    ...Typography.body,
    color: Colors.text.primary,
    fontWeight: '500',
    marginBottom: 2,
  },

  menuSubtitle: {
    ...Typography.caption,
    color: Colors.text.secondary,
  },

  menuItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  arrowWithComponent: {
    marginLeft: Spacing.sm,
  },

  logoutContainer: {
    marginHorizontal: Spacing.md,
    marginTop: Spacing.lg,
  },

  logoutButton: {
    borderColor: Colors.status.error,
  },
});
