import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
  StatusBar,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors, Typography, Spacing, Layout, Shadow } from '../constants';

export interface HeaderProps {
  title?: string;
  subtitle?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onLeftPress?: () => void;
  onRightPress?: () => void;
  variant?: 'default' | 'gradient' | 'transparent';
  showBackButton?: boolean;
  centerTitle?: boolean;
  style?: ViewStyle;
  titleStyle?: TextStyle;
  subtitleStyle?: TextStyle;
  children?: React.ReactNode;
  gradientColors?: string[];
}

export const Header: React.FC<HeaderProps> = ({
  title,
  subtitle,
  leftIcon,
  rightIcon,
  onLeftPress,
  onRightPress,
  variant = 'default',
  showBackButton = false,
  centerTitle = true,
  style,
  titleStyle,
  subtitleStyle,
  children,
  gradientColors = Colors.gradient.primary,
}) => {
  const insets = useSafeAreaInsets();

  const headerStyle = [
    styles.header,
    {
      paddingTop: insets.top,
      height: Layout.headerHeight + insets.top,
    },
    styles[variant],
    style,
  ];

  const titleStyles = [
    styles.title,
    centerTitle && styles.centerTitle,
    titleStyle,
  ];

  const subtitleStyles = [
    styles.subtitle,
    centerTitle && styles.centerSubtitle,
    subtitleStyle,
  ];

  const renderLeftButton = () => {
    if (showBackButton || leftIcon) {
      return (
        <TouchableOpacity
          onPress={onLeftPress}
          style={styles.iconButton}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons
            name={showBackButton ? 'arrow-back' : leftIcon!}
            size={Layout.iconSize.md}
            color={variant === 'gradient' ? Colors.text.inverse : Colors.text.primary}
          />
        </TouchableOpacity>
      );
    }
    return <View style={styles.iconButton} />;
  };

  const renderRightButton = () => {
    if (rightIcon) {
      return (
        <TouchableOpacity
          onPress={onRightPress}
          style={styles.iconButton}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons
            name={rightIcon}
            size={Layout.iconSize.md}
            color={variant === 'gradient' ? Colors.text.inverse : Colors.text.primary}
          />
        </TouchableOpacity>
      );
    }
    return <View style={styles.iconButton} />;
  };

  const renderContent = () => (
    <View style={styles.content}>
      {renderLeftButton()}
      
      <View style={styles.titleContainer}>
        {children ? (
          children
        ) : (
          <>
            {title && <Text style={titleStyles}>{title}</Text>}
            {subtitle && <Text style={subtitleStyles}>{subtitle}</Text>}
          </>
        )}
      </View>
      
      {renderRightButton()}
    </View>
  );

  if (variant === 'gradient') {
    return (
      <>
        <StatusBar
          barStyle="light-content"
          backgroundColor="transparent"
          translucent
        />
        <LinearGradient
          colors={gradientColors}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={headerStyle}
        >
          {renderContent()}
        </LinearGradient>
      </>
    );
  }

  return (
    <>
      <StatusBar
        barStyle={variant === 'transparent' ? 'dark-content' : 'dark-content'}
        backgroundColor="transparent"
        translucent
      />
      <View style={headerStyle}>
        {renderContent()}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  header: {
    justifyContent: 'flex-end',
    paddingHorizontal: Spacing.md,
    paddingBottom: Spacing.sm,
  },

  default: {
    backgroundColor: Colors.background.primary,
    ...Shadow.small,
  },

  gradient: {
    // Gradient styles applied via LinearGradient
  },

  transparent: {
    backgroundColor: 'transparent',
  },

  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: Layout.headerHeight - Spacing.sm,
  },

  titleContainer: {
    flex: 1,
    marginHorizontal: Spacing.sm,
  },

  title: {
    ...Typography.h5,
    color: Colors.text.primary,
  },

  centerTitle: {
    textAlign: 'center',
  },

  subtitle: {
    ...Typography.caption,
    color: Colors.text.secondary,
    marginTop: 2,
  },

  centerSubtitle: {
    textAlign: 'center',
  },

  iconButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
