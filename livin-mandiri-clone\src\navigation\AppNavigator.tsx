import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { LoginScreen } from '../screens';
import { TabNavigator } from './TabNavigator';

export type RootStackParamList = {
  Login: undefined;
  MainTabs: undefined;
  TransactionDetail: { transaction: any };
  TransferConfirmation: { transferData: any };
  ContactPicker: undefined;
  AccountSwitcher: undefined;
  SplitBill: undefined;
  CurrencyExchange: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();

export const AppNavigator: React.FC = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Login"
        screenOptions={{
          headerShown: false,
          gestureEnabled: true,
          cardStyleInterpolator: ({ current, layouts }) => {
            return {
              cardStyle: {
                transform: [
                  {
                    translateX: current.progress.interpolate({
                      inputRange: [0, 1],
                      outputRange: [layouts.screen.width, 0],
                    }),
                  },
                ],
              },
            };
          },
        }}
      >
        <Stack.Screen
          name="Login"
          component={LoginScreen}
          options={{
            animationTypeForReplace: 'pop',
          }}
        />
        <Stack.Screen
          name="MainTabs"
          component={TabNavigator}
          options={{
            gestureEnabled: false,
          }}
        />
        {/* Modal screens can be added here */}
      </Stack.Navigator>
    </NavigationContainer>
  );
};
