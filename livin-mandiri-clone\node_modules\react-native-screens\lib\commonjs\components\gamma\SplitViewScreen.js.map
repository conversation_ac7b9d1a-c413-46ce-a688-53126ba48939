{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_SplitViewScreenNativeComponent", "e", "__esModule", "default", "SplitViewScreen", "children", "createElement", "style", "StyleSheet", "absoluteFill", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/gamma/SplitViewScreen.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,+BAAA,GAAAH,sBAAA,CAAAC,OAAA;AAA+F,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAW/F;AACA;AACA;AACA,SAASG,eAAeA,CAAC;EAAEC;AAA+B,CAAC,EAAE;EAC3D,oBACET,MAAA,CAAAO,OAAA,CAAAG,aAAA,CAACN,+BAAA,CAAAG,OAA8B;IAACI,KAAK,EAAEC,uBAAU,CAACC;EAAa,GAC5DJ,QAC6B,CAAC;AAErC;AAAC,IAAAK,QAAA,GAAAC,OAAA,CAAAR,OAAA,GAEcC,eAAe", "ignoreList": []}