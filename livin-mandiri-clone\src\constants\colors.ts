// Livin' by Mandiri Color Palette
// Based on research of the official app design

export const Colors = {
  // Primary Colors - Blues (Mandiri Brand Colors)
  primary: {
    main: '#003D7A',        // Deep Mandiri Blue
    light: '#1E5AA8',       // Lighter blue for gradients
    lighter: '#4A90E2',     // Even lighter blue
    dark: '#002952',        // Darker blue for depth
  },

  // Secondary Colors
  secondary: {
    main: '#0066CC',        // Bright blue for accents
    light: '#3399FF',       // Light blue for highlights
    dark: '#004499',        // Dark blue for contrast
  },

  // Gradient Colors
  gradient: {
    primary: ['#003D7A', '#1E5AA8'],     // Main blue gradient
    secondary: ['#4A90E2', '#0066CC'],   // Secondary gradient
    light: ['#E8F4FD', '#FFFFFF'],      // Light gradient for cards
    accent: ['#1E5AA8', '#4A90E2'],     // Accent gradient
  },

  // Neutral Colors
  neutral: {
    white: '#FFFFFF',
    gray50: '#F8F9FA',      // Very light gray for backgrounds
    gray100: '#F1F3F4',     // Light gray for card backgrounds
    gray200: '#E8EAED',     // Border gray
    gray300: '#DADCE0',     // Divider gray
    gray400: '#9AA0A6',     // Placeholder text
    gray500: '#5F6368',     // Secondary text
    gray600: '#3C4043',     // Primary text
    gray700: '#202124',     // Dark text
    gray800: '#1A1A1A',     // Very dark text
    black: '#000000',
  },

  // Status Colors
  status: {
    success: '#34A853',     // Green for success states
    warning: '#FBBC04',     // Yellow for warnings
    error: '#EA4335',       // Red for errors
    info: '#4285F4',        // Blue for info
  },

  // Background Colors
  background: {
    primary: '#FFFFFF',     // Main background
    secondary: '#F8F9FA',   // Secondary background
    card: '#FFFFFF',        // Card background
    modal: 'rgba(0, 0, 0, 0.5)', // Modal overlay
  },

  // Text Colors
  text: {
    primary: '#202124',     // Main text color
    secondary: '#5F6368',   // Secondary text
    tertiary: '#9AA0A6',    // Tertiary text
    inverse: '#FFFFFF',     // White text for dark backgrounds
    link: '#1E5AA8',        // Link color
    placeholder: '#9AA0A6', // Placeholder text
  },

  // Border Colors
  border: {
    light: '#E8EAED',       // Light borders
    medium: '#DADCE0',      // Medium borders
    dark: '#9AA0A6',        // Dark borders
    focus: '#1E5AA8',       // Focus state borders
  },

  // Shadow Colors
  shadow: {
    light: 'rgba(0, 0, 0, 0.1)',
    medium: 'rgba(0, 0, 0, 0.15)',
    dark: 'rgba(0, 0, 0, 0.25)',
    blue: 'rgba(30, 90, 168, 0.15)', // Blue shadow for primary elements
  },
} as const;

// Color utility functions
export const getGradientString = (colors: string[]) => {
  return `linear-gradient(135deg, ${colors.join(', ')})`;
};

export const hexToRgba = (hex: string, alpha: number) => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};
