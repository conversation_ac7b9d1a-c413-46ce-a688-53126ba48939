// Livin' by Mandiri Spacing and Layout System

import { Dimensions } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Base spacing unit (8px system)
const BASE_UNIT = 8;

// Spacing scale
export const Spacing = {
  xs: BASE_UNIT * 0.5,    // 4px
  sm: BASE_UNIT,          // 8px
  md: BASE_UNIT * 2,      // 16px
  lg: BASE_UNIT * 3,      // 24px
  xl: BASE_UNIT * 4,      // 32px
  '2xl': BASE_UNIT * 5,   // 40px
  '3xl': BASE_UNIT * 6,   // 48px
  '4xl': BASE_UNIT * 8,   // 64px
  '5xl': BASE_UNIT * 10,  // 80px
} as const;

// Border radius values
export const BorderRadius = {
  none: 0,
  xs: 4,
  sm: 6,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 20,
  '3xl': 24,
  full: 9999,
} as const;

// Screen dimensions
export const Screen = {
  width: screenWidth,
  height: screenHeight,
  isSmall: screenWidth < 375,
  isMedium: screenWidth >= 375 && screenWidth < 414,
  isLarge: screenWidth >= 414,
} as const;

// Layout constants
export const Layout = {
  // Container padding
  containerPadding: Spacing.md,
  containerPaddingHorizontal: Spacing.md,
  containerPaddingVertical: Spacing.lg,
  
  // Card spacing
  cardPadding: Spacing.md,
  cardMargin: Spacing.sm,
  cardBorderRadius: BorderRadius.lg,
  
  // Header heights
  headerHeight: 56,
  tabBarHeight: 60,
  statusBarHeight: 44, // iOS default, adjust for Android
  
  // Button dimensions
  buttonHeight: {
    small: 36,
    medium: 44,
    large: 52,
  },
  
  buttonPadding: {
    small: Spacing.sm,
    medium: Spacing.md,
    large: Spacing.lg,
  },
  
  // Input field dimensions
  inputHeight: 48,
  inputPadding: Spacing.md,
  inputBorderRadius: BorderRadius.md,
  
  // Icon sizes
  iconSize: {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 32,
    xl: 40,
    '2xl': 48,
  },
  
  // Avatar sizes
  avatarSize: {
    xs: 24,
    sm: 32,
    md: 40,
    lg: 48,
    xl: 64,
    '2xl': 80,
  },
  
  // Section spacing
  sectionSpacing: Spacing.lg,
  sectionPadding: Spacing.md,
  
  // List item spacing
  listItemHeight: 56,
  listItemPadding: Spacing.md,
  
  // Modal dimensions
  modalPadding: Spacing.lg,
  modalBorderRadius: BorderRadius.xl,
  
  // Safe area insets (will be overridden by actual safe area)
  safeAreaTop: 44,
  safeAreaBottom: 34,
} as const;

// Shadow presets
export const Shadow = {
  none: {
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  
  small: {
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  
  medium: {
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  
  large: {
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
  
  xl: {
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 16,
  },
} as const;

// Responsive breakpoints
export const Breakpoints = {
  sm: 375,
  md: 414,
  lg: 768,
  xl: 1024,
} as const;

// Utility functions
export const getResponsiveSpacing = (size: keyof typeof Spacing) => {
  const baseSpacing = Spacing[size];
  if (Screen.isSmall) return baseSpacing * 0.8;
  if (Screen.isLarge) return baseSpacing * 1.2;
  return baseSpacing;
};

export const getResponsiveFontSize = (size: number) => {
  if (Screen.isSmall) return size * 0.9;
  if (Screen.isLarge) return size * 1.1;
  return size;
};

// Grid system
export const Grid = {
  columns: 12,
  gutter: Spacing.md,
  margin: Spacing.md,
  
  // Column widths (percentage based)
  col1: '8.333%',
  col2: '16.666%',
  col3: '25%',
  col4: '33.333%',
  col5: '41.666%',
  col6: '50%',
  col7: '58.333%',
  col8: '66.666%',
  col9: '75%',
  col10: '83.333%',
  col11: '91.666%',
  col12: '100%',
} as const;
