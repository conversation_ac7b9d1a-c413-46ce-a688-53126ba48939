import React, { createContext, useContext, useReducer, ReactNode } from 'react';

// Types
interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  memberSince: string;
  accountType: string;
}

interface Account {
  id: string;
  name: string;
  number: string;
  balance: number;
  type: 'savings' | 'checking' | 'credit';
}

interface AppState {
  user: User | null;
  accounts: Account[];
  selectedAccount: Account | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  notifications: any[];
}

// Actions
type AppAction =
  | { type: 'SET_USER'; payload: User }
  | { type: 'SET_ACCOUNTS'; payload: Account[] }
  | { type: 'SET_SELECTED_ACCOUNT'; payload: Account }
  | { type: 'SET_AUTHENTICATED'; payload: boolean }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'ADD_NOTIFICATION'; payload: any }
  | { type: 'REMOVE_NOTIFICATION'; payload: string }
  | { type: 'LOGOUT' };

// Initial state
const initialState: AppState = {
  user: null,
  accounts: [],
  selectedAccount: null,
  isAuthenticated: false,
  isLoading: false,
  notifications: [],
};

// Reducer
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
      };
    case 'SET_ACCOUNTS':
      return {
        ...state,
        accounts: action.payload,
        selectedAccount: action.payload[0] || null,
      };
    case 'SET_SELECTED_ACCOUNT':
      return {
        ...state,
        selectedAccount: action.payload,
      };
    case 'SET_AUTHENTICATED':
      return {
        ...state,
        isAuthenticated: action.payload,
      };
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    case 'ADD_NOTIFICATION':
      return {
        ...state,
        notifications: [...state.notifications, action.payload],
      };
    case 'REMOVE_NOTIFICATION':
      return {
        ...state,
        notifications: state.notifications.filter(
          (notification) => notification.id !== action.payload
        ),
      };
    case 'LOGOUT':
      return {
        ...initialState,
      };
    default:
      return state;
  }
};

// Context
interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  // Helper functions
  login: (user: User, accounts: Account[]) => void;
  logout: () => void;
  switchAccount: (account: Account) => void;
  setLoading: (loading: boolean) => void;
  addNotification: (notification: any) => void;
  removeNotification: (id: string) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider
interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Helper functions
  const login = (user: User, accounts: Account[]) => {
    dispatch({ type: 'SET_USER', payload: user });
    dispatch({ type: 'SET_ACCOUNTS', payload: accounts });
    dispatch({ type: 'SET_AUTHENTICATED', payload: true });
  };

  const logout = () => {
    dispatch({ type: 'LOGOUT' });
  };

  const switchAccount = (account: Account) => {
    dispatch({ type: 'SET_SELECTED_ACCOUNT', payload: account });
  };

  const setLoading = (loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  };

  const addNotification = (notification: any) => {
    dispatch({ type: 'ADD_NOTIFICATION', payload: notification });
  };

  const removeNotification = (id: string) => {
    dispatch({ type: 'REMOVE_NOTIFICATION', payload: id });
  };

  const value: AppContextType = {
    state,
    dispatch,
    login,
    logout,
    switchAccount,
    setLoading,
    addNotification,
    removeNotification,
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

// Hook
export const useApp = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

// Export types
export type { User, Account, AppState, AppAction };
