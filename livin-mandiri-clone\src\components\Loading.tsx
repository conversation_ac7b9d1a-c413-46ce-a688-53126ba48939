import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  StyleSheet,
  Animated,
  Dimensions,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors, Typography, Spacing, BorderRadius } from '../constants';

const { width, height } = Dimensions.get('window');

export interface LoadingProps {
  visible?: boolean;
  text?: string;
  variant?: 'overlay' | 'inline' | 'card';
  size?: 'small' | 'medium' | 'large';
  color?: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
  overlay?: boolean;
}

export const Loading: React.FC<LoadingProps> = ({
  visible = true,
  text,
  variant = 'overlay',
  size = 'medium',
  color = Colors.primary.main,
  style,
  textStyle,
  overlay = true,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, fadeAnim, scaleAnim]);

  const getIndicatorSize = () => {
    switch (size) {
      case 'small':
        return 20;
      case 'medium':
        return 30;
      case 'large':
        return 40;
      default:
        return 30;
    }
  };

  const renderLoadingContent = () => (
    <View style={styles.content}>
      <ActivityIndicator size={getIndicatorSize()} color={color} />
      {text && (
        <Text style={[styles.text, textStyle]}>
          {text}
        </Text>
      )}
    </View>
  );

  const renderGradientLoading = () => (
    <LinearGradient
      colors={Colors.gradient.primary}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={styles.gradientContainer}
    >
      <ActivityIndicator size={getIndicatorSize()} color={Colors.text.inverse} />
      {text && (
        <Text style={[styles.text, styles.gradientText, textStyle]}>
          {text}
        </Text>
      )}
    </LinearGradient>
  );

  if (!visible) {
    return null;
  }

  if (variant === 'inline') {
    return (
      <View style={[styles.inline, style]}>
        {renderLoadingContent()}
      </View>
    );
  }

  if (variant === 'card') {
    return (
      <Animated.View
        style={[
          styles.card,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
          style,
        ]}
      >
        {renderGradientLoading()}
      </Animated.View>
    );
  }

  // Overlay variant
  return (
    <Animated.View
      style={[
        styles.overlay,
        {
          opacity: fadeAnim,
        },
        style,
      ]}
    >
      <Animated.View
        style={[
          styles.container,
          {
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        {renderGradientLoading()}
      </Animated.View>
    </Animated.View>
  );
};

// Skeleton Loading Component for list items
export interface SkeletonProps {
  width?: number | string;
  height?: number;
  borderRadius?: number;
  style?: ViewStyle;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  style,
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ])
    );
    animation.start();

    return () => animation.stop();
  }, [animatedValue]);

  const backgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [Colors.neutral.gray200, Colors.neutral.gray300],
  });

  return (
    <Animated.View
      style={[
        {
          width,
          height,
          borderRadius,
          backgroundColor,
        },
        style,
      ]}
    />
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.background.modal,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
  },

  container: {
    backgroundColor: Colors.background.card,
    borderRadius: BorderRadius.xl,
    padding: Spacing.xl,
    minWidth: 120,
    alignItems: 'center',
    justifyContent: 'center',
  },

  gradientContainer: {
    borderRadius: BorderRadius.xl,
    padding: Spacing.xl,
    minWidth: 120,
    alignItems: 'center',
    justifyContent: 'center',
  },

  card: {
    backgroundColor: Colors.background.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
    margin: Spacing.md,
    elevation: 4,
    shadowColor: Colors.shadow.medium,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },

  inline: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.md,
  },

  content: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  text: {
    ...Typography.body,
    color: Colors.text.secondary,
    marginTop: Spacing.sm,
    textAlign: 'center',
  },

  gradientText: {
    color: Colors.text.inverse,
  },
});
