{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_react", "_interopRequireDefault", "require", "_BottomTabsScreenNativeComponent", "_reactNative", "_reactFreeze", "_core", "_flags", "e", "__esModule", "_extends", "assign", "bind", "n", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "BottomTabsScreen", "props", "componentNodeRef", "React", "useRef", "componentNodeHandle", "useEffect", "current", "findNodeHandle", "nativeViewIsVisible", "setNativeViewIsVisible", "useState", "onWillAppear", "onDidAppear", "onWillDisappear", "onDidDisappear", "isFocused", "icon", "selectedIcon", "rest", "shouldFreeze", "freezeEnabled", "featureFlags", "experiment", "controlledBottomTabs", "onWillAppearCallback", "useCallback", "event", "console", "log", "onDidAppearCallback", "onWillDisappearCallback", "onDidDisappearCallback", "info", "tabKey", "iconProps", "parseIconsToNativeProps", "createElement", "collapsable", "style", "styles", "fillParent", "ref", "Freeze", "freeze", "placeholder", "children", "parseIconToNativeProps", "iconType", "iconSfSymbolName", "sfSymbolName", "iconImageSource", "imageSource", "templateSource", "Error", "selectedIconImageSource", "selectedIconSfSymbolName", "selectedIconType", "undefined", "_default", "StyleSheet", "create", "position", "flex", "width", "height"], "sourceRoot": "../../../src", "sources": ["components/BottomTabsScreen.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAEb,IAAAC,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,gCAAA,GAAAF,sBAAA,CAAAC,OAAA;AAKA,IAAAE,YAAA,GAAAF,OAAA;AASA,IAAAG,YAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAAwC,SAAAD,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAT,OAAA,EAAAS,CAAA;AAAA,SAAAE,SAAA,WAAAA,QAAA,GAAAf,MAAA,CAAAgB,MAAA,GAAAhB,MAAA,CAAAgB,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAL,CAAA,MAAAA,CAAA,GAAAM,SAAA,CAAAC,MAAA,EAAAP,CAAA,UAAAQ,CAAA,GAAAF,SAAA,CAAAN,CAAA,YAAAS,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAJ,CAAA,CAAAI,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAJ,CAAA,KAAAH,QAAA,CAAAU,KAAA,OAAAN,SAAA;AAOxC;;AASA;;AAKA;;AA2DA;AACA;AACA;AACA,SAASO,gBAAgBA,CAACC,KAA4B,EAAE;EACtD,MAAMC,gBAAgB,GAAGC,cAAK,CAACC,MAAM,CAA+B,IAAI,CAAC;EACzE,MAAMC,mBAAmB,GAAGF,cAAK,CAACC,MAAM,CAAS,CAAC,CAAC,CAAC;EAEpDD,cAAK,CAACG,SAAS,CAAC,MAAM;IACpB,IAAIJ,gBAAgB,CAACK,OAAO,IAAI,IAAI,EAAE;MACpCF,mBAAmB,CAACE,OAAO,GACzB,IAAAC,2BAAc,EAACN,gBAAgB,CAACK,OAAO,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,MAAM;MACLF,mBAAmB,CAACE,OAAO,GAAG,CAAC,CAAC;IAClC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM,CAACE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGP,cAAK,CAACQ,QAAQ,CAAC,KAAK,CAAC;EAE3E,MAAM;IACJC,YAAY;IACZC,WAAW;IACXC,eAAe;IACfC,cAAc;IACdC,SAAS,GAAG,KAAK;IACjBC,IAAI;IACJC,YAAY;IACZ,GAAGC;EACL,CAAC,GAAGlB,KAAK;EAET,IAAImB,YAAY,GAAG,IAAAC,mBAAa,EAAC,CAAC;EAElC,IAAIC,mBAAY,CAACC,UAAU,CAACC,oBAAoB,EAAE;IAChD;IACAJ,YAAY,GAAGA,YAAY,IAAI,CAACX,mBAAmB,IAAI,CAACO,SAAS;EACnE,CAAC,MAAM;IACLI,YAAY,GAAGA,YAAY,IAAI,CAACX,mBAAmB;EACrD;EAEA,MAAMgB,oBAAoB,GAAGtB,cAAK,CAACuB,WAAW,CAC3CC,KAAwC,IAAK;IAC5CC,OAAO,CAACC,GAAG,CACT,eAAexB,mBAAmB,CAACE,OAAO,yBAC5C,CAAC;IACDG,sBAAsB,CAAC,IAAI,CAAC;IAC5BE,YAAY,GAAGe,KAAK,CAAC;EACvB,CAAC,EACD,CAACf,YAAY,CACf,CAAC;EAED,MAAMkB,mBAAmB,GAAG3B,cAAK,CAACuB,WAAW,CAC1CC,KAAwC,IAAK;IAC5CC,OAAO,CAACC,GAAG,CACT,eAAexB,mBAAmB,CAACE,OAAO,wBAC5C,CAAC;IACDM,WAAW,GAAGc,KAAK,CAAC;EACtB,CAAC,EACD,CAACd,WAAW,CACd,CAAC;EAED,MAAMkB,uBAAuB,GAAG5B,cAAK,CAACuB,WAAW,CAC9CC,KAAwC,IAAK;IAC5CC,OAAO,CAACC,GAAG,CACT,eAAexB,mBAAmB,CAACE,OAAO,4BAC5C,CAAC;IACDO,eAAe,GAAGa,KAAK,CAAC;EAC1B,CAAC,EACD,CAACb,eAAe,CAClB,CAAC;EAED,MAAMkB,sBAAsB,GAAG7B,cAAK,CAACuB,WAAW,CAC7CC,KAAwC,IAAK;IAC5CC,OAAO,CAACC,GAAG,CACT,eAAexB,mBAAmB,CAACE,OAAO,2BAC5C,CAAC;IACDG,sBAAsB,CAAC,KAAK,CAAC;IAC7BK,cAAc,GAAGY,KAAK,CAAC;EACzB,CAAC,EACD,CAACZ,cAAc,CACjB,CAAC;EAEDa,OAAO,CAACK,IAAI,CACV,eAAe5B,mBAAmB,CAACE,OAAO,IAAI,CAAC,CAAC,qBAC9CY,IAAI,CAACe,MAAM,kBACKd,YAAY,gBAAgBJ,SAAS,yBAAyBP,mBAAmB,EACrG,CAAC;EAED,MAAM0B,SAAS,GAAGC,uBAAuB,CAACnB,IAAI,EAAEC,YAAY,CAAC;EAE7D,oBACEvC,MAAA,CAAAD,OAAA,CAAA2D,aAAA,CAACvD,gCAAA,CAAAJ,OAA+B,EAAAW,QAAA;IAC9BiD,WAAW,EAAE,KAAM;IACnBC,KAAK,EAAEC,MAAM,CAACC,UAAW;IACzB7B,YAAY,EAAEa,oBAAqB;IACnCZ,WAAW,EAAEiB,mBAAoB;IACjChB,eAAe,EAAEiB,uBAAwB;IACzChB,cAAc,EAAEiB,sBAAuB;IACvChB,SAAS,EAAEA;EAAU,GACjBmB,SAAS;IACb;IACAO,GAAG,EAAExC;EAAiB,GAClBiB,IAAI,gBACRxC,MAAA,CAAAD,OAAA,CAAA2D,aAAA,CAACrD,YAAA,CAAA2D,MAAM;IAACC,MAAM,EAAExB,YAAa;IAACyB,WAAW,EAAE1B,IAAI,CAAC0B;EAAY,GACzD1B,IAAI,CAAC2B,QACA,CACuB,CAAC;AAEtC;AAEA,SAASC,sBAAsBA,CAAC9B,IAAsB,EAIpD;EACA,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,CAAC,CAAC;EACX;EAEA,IAAI,cAAc,IAAIA,IAAI,EAAE;IAC1B;IACA,OAAO;MACL+B,QAAQ,EAAE,UAAU;MACpBC,gBAAgB,EAAEhC,IAAI,CAACiC;IACzB,CAAC;EACH,CAAC,MAAM,IAAI,aAAa,IAAIjC,IAAI,EAAE;IAChC,OAAO;MACL+B,QAAQ,EAAE,OAAO;MACjBG,eAAe,EAAElC,IAAI,CAACmC;IACxB,CAAC;EACH,CAAC,MAAM,IAAI,gBAAgB,IAAInC,IAAI,EAAE;IACnC;IACA,OAAO;MACL+B,QAAQ,EAAE,UAAU;MACpBG,eAAe,EAAElC,IAAI,CAACoC;IACxB,CAAC;EACH,CAAC,MAAM;IACL;IACA,MAAM,IAAIC,KAAK,CACb,kGACF,CAAC;EACH;AACF;AAEA,SAASlB,uBAAuBA,CAC9BnB,IAAsB,EACtBC,YAA8B,EAO9B;EACA,MAAM;IAAEiC,eAAe;IAAEF,gBAAgB;IAAED;EAAS,CAAC,GACnDD,sBAAsB,CAAC9B,IAAI,CAAC;EAC9B,MAAM;IACJkC,eAAe,EAAEI,uBAAuB;IACxCN,gBAAgB,EAAEO,wBAAwB;IAC1CR,QAAQ,EAAES;EACZ,CAAC,GAAGV,sBAAsB,CAAC7B,YAAY,CAAC;EAExC,IACE8B,QAAQ,KAAKU,SAAS,IACtBD,gBAAgB,KAAKC,SAAS,IAC9BV,QAAQ,KAAKS,gBAAgB,EAC7B;IACA,MAAM,IAAIH,KAAK,CAAC,sDAAsD,CAAC;EACzE,CAAC,MAAM,IAAIN,QAAQ,KAAKU,SAAS,IAAID,gBAAgB,KAAKC,SAAS,EAAE;IACnE;IACA,MAAM,IAAIJ,KAAK,CACb,4EACF,CAAC;EACH;EAEA,OAAO;IACLN,QAAQ;IACRG,eAAe;IACfF,gBAAgB;IAChBM,uBAAuB;IACvBC;EACF,CAAC;AACH;AAAC,IAAAG,QAAA,GAAAnF,OAAA,CAAAE,OAAA,GAEcsB,gBAAgB;AAE/B,MAAMwC,MAAM,GAAGoB,uBAAU,CAACC,MAAM,CAAC;EAC/BpB,UAAU,EAAE;IACVqB,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE;EACV;AACF,CAAC,CAAC", "ignoreList": []}