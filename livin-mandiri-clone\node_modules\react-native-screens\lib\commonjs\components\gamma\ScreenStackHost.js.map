{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_ScreenStackHostNativeComponent", "e", "__esModule", "default", "ScreenStackHost", "children", "createElement", "style", "styles", "container", "StyleSheet", "create", "flex", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/gamma/ScreenStackHost.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAGA,IAAAE,+BAAA,GAAAH,sBAAA,CAAAC,OAAA;AAA+F,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAU/F;AACA;AACA;AACA,SAASG,eAAeA,CAAC;EAAEC;AAA+B,CAAC,EAAE;EAC3D,oBACET,MAAA,CAAAO,OAAA,CAAAG,aAAA,CAACN,+BAAA,CAAAG,OAA8B;IAACI,KAAK,EAAEC,MAAM,CAACC;EAAU,GACrDJ,QAC6B,CAAC;AAErC;AAEA,MAAMG,MAAM,GAAGE,uBAAU,CAACC,MAAM,CAAC;EAC/BF,SAAS,EAAE;IACTG,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAX,OAAA,GAEYC,eAAe", "ignoreList": []}