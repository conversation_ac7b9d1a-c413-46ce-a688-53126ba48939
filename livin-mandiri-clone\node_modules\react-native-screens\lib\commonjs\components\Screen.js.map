{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "ScreenContext", "InnerScreen", "_react", "_interopRequireDefault", "require", "_reactNative", "_TransitionProgressContext", "_DelayedFreeze", "_core", "_ScreenNativeComponent", "_ModalScreenNativeComponent", "_usePrevious", "_edgeToEdge", "_sheet", "e", "__esModule", "_extends", "assign", "bind", "n", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "AnimatedNativeScreen", "Animated", "createAnimatedComponent", "ScreenNativeComponent", "AnimatedNativeModalScreen", "ModalScreenNativeComponent", "React", "forwardRef", "props", "ref", "innerRef", "useRef", "useImperativeHandle", "current", "prevActivityState", "usePrevious", "activityState", "setRef", "onComponentRef", "closing", "Value", "progress", "goingForward", "enabled", "screensEnabled", "freezeOnBlur", "freezeEnabled", "shouldFreeze", "rest", "sheetAllowedDetents", "sheetLargestUndimmedDetentIndex", "SHEET_DIMMED_ALWAYS", "sheetGrabberVisible", "sheetCornerRadius", "sheetExpandsWhenScrolledToEdge", "sheetElevation", "sheetInitialDetentIndex", "screenId", "stackPresentation", "onAppear", "onDisappear", "onWillAppear", "onWillDisappear", "isNativePlatformSupported", "resolvedSheetAllowedDetents", "resolveSheetAllowedDetents", "resolvedSheetLargestUndimmedDetent", "resolveSheetLargestUndimmedDetent", "resolvedSheetInitialDetentIndex", "resolveSheetInitialDetentIndex", "shouldUseModalScreenComponent", "Platform", "select", "ios", "undefined", "android", "AnimatedScreen", "active", "children", "isNativeStack", "gestureResponseDistance", "onGestureCancel", "style", "console", "warn", "Error", "handleRef", "viewConfig", "validAttributes", "display", "_viewConfig", "freeze", "createElement", "zIndex", "sheetLargestUndimmedDetent", "sheetInitialDetent", "start", "end", "top", "bottom", "onTransitionProgress", "event", "nativeEvent", "useNativeDriver", "Provider", "View", "createContext", "Screen", "ScreenWrapper", "useContext", "EDGE_TO_EDGE", "transformEdgeToEdgeProps", "displayName", "_default"], "sourceRoot": "../../../src", "sources": ["components/Screen.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA,GAAAF,OAAA,CAAAG,aAAA,GAAAH,OAAA,CAAAI,WAAA;AAEb,IAAAC,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,0BAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,cAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAGA,IAAAI,KAAA,GAAAJ,OAAA;AAOA,IAAAK,sBAAA,GAAAN,sBAAA,CAAAC,OAAA;AAGA,IAAAM,2BAAA,GAAAP,sBAAA,CAAAC,OAAA;AAIA,IAAAO,YAAA,GAAAP,OAAA;AACA,IAAAQ,WAAA,GAAAR,OAAA;AACA,IAAAS,MAAA,GAAAT,OAAA;AAKyB,SAAAD,uBAAAW,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAf,OAAA,EAAAe,CAAA;AAAA,SAAAE,SAAA,WAAAA,QAAA,GAAArB,MAAA,CAAAsB,MAAA,GAAAtB,MAAA,CAAAsB,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAL,CAAA,MAAAA,CAAA,GAAAM,SAAA,CAAAC,MAAA,EAAAP,CAAA,UAAAQ,CAAA,GAAAF,SAAA,CAAAN,CAAA,YAAAS,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAJ,CAAA,CAAAI,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAJ,CAAA,KAAAH,QAAA,CAAAU,KAAA,OAAAN,SAAA,KAfzB;AAkBA,MAAMO,oBAAoB,GAAGC,qBAAQ,CAACC,uBAAuB,CAC3DC,8BACF,CAAC;AACD,MAAMC,yBAAyB,GAAGH,qBAAQ,CAACC,uBAAuB,CAChEG,mCACF,CAAC;;AAED;AACA;;AAkBO,MAAM/B,WAAW,GAAAJ,OAAA,CAAAI,WAAA,gBAAGgC,cAAK,CAACC,UAAU,CACzC,SAASjC,WAAWA,CAACkC,KAAK,EAAEC,GAAG,EAAE;EAC/B,MAAMC,QAAQ,GAAGJ,cAAK,CAACK,MAAM,CAAoB,IAAI,CAAC;EACtDL,cAAK,CAACM,mBAAmB,CAACH,GAAG,EAAE,MAAMC,QAAQ,CAACG,OAAQ,EAAE,EAAE,CAAC;EAC3D,MAAMC,iBAAiB,GAAG,IAAAC,wBAAW,EAACP,KAAK,CAACQ,aAAa,CAAC;EAE1D,MAAMC,MAAM,GAAIR,GAAe,IAAK;IAClCC,QAAQ,CAACG,OAAO,GAAGJ,GAAG;IACtBD,KAAK,CAACU,cAAc,GAAGT,GAAG,CAAC;EAC7B,CAAC;EAED,MAAMU,OAAO,GAAGb,cAAK,CAACK,MAAM,CAAC,IAAIV,qBAAQ,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACP,OAAO;EAC3D,MAAMQ,QAAQ,GAAGf,cAAK,CAACK,MAAM,CAAC,IAAIV,qBAAQ,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACP,OAAO;EAC5D,MAAMS,YAAY,GAAGhB,cAAK,CAACK,MAAM,CAAC,IAAIV,qBAAQ,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACP,OAAO;EAEhE,MAAM;IACJU,OAAO,GAAG,IAAAC,oBAAc,EAAC,CAAC;IAC1BC,YAAY,GAAG,IAAAC,mBAAa,EAAC,CAAC;IAC9BC,YAAY;IACZ,GAAGC;EACL,CAAC,GAAGpB,KAAK;;EAET;EACA;EACA,MAAM;IACJ;IACAqB,mBAAmB,GAAG,CAAC,GAAG,CAAC;IAC3BC,+BAA+B,GAAGC,0BAAmB;IACrDC,mBAAmB,GAAG,KAAK;IAC3BC,iBAAiB,GAAG,CAAC,GAAG;IACxBC,8BAA8B,GAAG,IAAI;IACrCC,cAAc,GAAG,EAAE;IACnBC,uBAAuB,GAAG,CAAC;IAC3B;IACAC,QAAQ;IACRC,iBAAiB;IACjB;IACAC,QAAQ;IACRC,WAAW;IACXC,YAAY;IACZC;EACF,CAAC,GAAGd,IAAI;EAER,IAAIL,OAAO,IAAIoB,+BAAyB,EAAE;IACxC,MAAMC,2BAA2B,GAC/B,IAAAC,iCAA0B,EAAChB,mBAAmB,CAAC;IACjD,MAAMiB,kCAAkC,GACtC,IAAAC,wCAAiC,EAC/BjB,+BAA+B,EAC/Bc,2BAA2B,CAAClD,MAAM,GAAG,CACvC,CAAC;IACH,MAAMsD,+BAA+B,GAAG,IAAAC,qCAA8B,EACpEb,uBAAuB,EACvBQ,2BAA2B,CAAClD,MAAM,GAAG,CACvC,CAAC;;IAED;IACA;IACA,MAAMwD,6BAA6B,GAAGC,qBAAQ,CAACC,MAAM,CAAC;MACpDC,GAAG,EAAE,EACHf,iBAAiB,KAAKgB,SAAS,IAC/BhB,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,gBAAgB,IACtCA,iBAAiB,KAAK,2BAA2B,CAClD;MACDiB,OAAO,EAAE,KAAK;MACdnF,OAAO,EAAE;IACX,CAAC,CAAC;IAEF,MAAMoF,cAAc,GAAGN,6BAA6B,GAChD9C,yBAAyB,GACzBJ,oBAAoB;IAExB,IAAI;MACF;MACA;MACA;MACAyD,MAAM;MACNzC,aAAa;MACb0C,QAAQ;MACRC,aAAa;MACbC,uBAAuB;MACvBC,eAAe;MACfC,KAAK;MACL,GAAGtD;IACL,CAAC,GAAGoB,IAAI;IAER,IAAI6B,MAAM,KAAKH,SAAS,IAAItC,aAAa,KAAKsC,SAAS,EAAE;MACvDS,OAAO,CAACC,IAAI,CACV,+QACF,CAAC;MACDhD,aAAa,GAAGyC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACxC;IAEA,IACEE,aAAa,IACb7C,iBAAiB,KAAKwC,SAAS,IAC/BtC,aAAa,KAAKsC,SAAS,EAC3B;MACA,IAAIxC,iBAAiB,GAAGE,aAAa,EAAE;QACrC,MAAM,IAAIiD,KAAK,CACb,8DACF,CAAC;MACH;IACF;IAEA,MAAMC,SAAS,GAAIzD,GAAe,IAAK;MACrC;MACA;MACA,IAAIA,GAAG,EAAE0D,UAAU,EAAEC,eAAe,EAAEN,KAAK,EAAE;QAC3CrD,GAAG,CAAC0D,UAAU,CAACC,eAAe,CAACN,KAAK,GAAG;UACrC,GAAGrD,GAAG,CAAC0D,UAAU,CAACC,eAAe,CAACN,KAAK;UACvCO,OAAO,EAAE;QACX,CAAC;QACDpD,MAAM,CAACR,GAAG,CAAC;MACb,CAAC,MAAM,IAAIA,GAAG,EAAE6D,WAAW,EAAEF,eAAe,EAAEN,KAAK,EAAE;QACnDrD,GAAG,CAAC6D,WAAW,CAACF,eAAe,CAACN,KAAK,GAAG;UACtC,GAAGrD,GAAG,CAAC6D,WAAW,CAACF,eAAe,CAACN,KAAK;UACxCO,OAAO,EAAE;QACX,CAAC;QACDpD,MAAM,CAACR,GAAG,CAAC;MACb;IACF,CAAC;IAED,MAAM8D,MAAM,GACV9C,YAAY,KACXE,YAAY,KAAK2B,SAAS,GAAG3B,YAAY,GAAGX,aAAa,KAAK,CAAC,CAAC;IAEnE,oBACEzC,MAAA,CAAAH,OAAA,CAAAoG,aAAA,CAAC5F,cAAA,CAAAR,OAAa;MAACmG,MAAM,EAAEA;IAAO,gBAC5BhG,MAAA,CAAAH,OAAA,CAAAoG,aAAA,CAAChB,cAAc,EAAAnE,QAAA,KACTmB,KAAK;MACT;AACZ;AACA;AACA;AACA;MACY+B,QAAQ,EAAEA,QAAoC;MAC9CC,WAAW,EAAEA,WAA0C;MACvDC,YAAY,EAAEA,YAA4C;MAC1DC,eAAe,EAAEA,eAAkD;MACnEmB,eAAe,EACZA,eAAe,KACf,MAAM;QACL;MAAA,CACD;MAEH;MACA;MACA;MACA;MACA;MAAA;MACAC,KAAK,EAAE,CAACA,KAAK,EAAE;QAAEW,MAAM,EAAEnB;MAAU,CAAC,CAAE;MACtCtC,aAAa,EAAEA,aAAc;MAC7BqB,QAAQ,EAAEA,QAAS;MACnBR,mBAAmB,EAAEe,2BAA4B;MACjD8B,0BAA0B,EAAE5B,kCAAmC;MAC/DX,cAAc,EAAEA,cAAe;MAC/BH,mBAAmB,EAAEA,mBAAoB;MACzCC,iBAAiB,EAAEA,iBAAkB;MACrCC,8BAA8B,EAAEA,8BAA+B;MAC/DyC,kBAAkB,EAAE3B,+BAAgC;MACpDY,uBAAuB,EAAE;QACvBgB,KAAK,EAAEhB,uBAAuB,EAAEgB,KAAK,IAAI,CAAC,CAAC;QAC3CC,GAAG,EAAEjB,uBAAuB,EAAEiB,GAAG,IAAI,CAAC,CAAC;QACvCC,GAAG,EAAElB,uBAAuB,EAAEkB,GAAG,IAAI,CAAC,CAAC;QACvCC,MAAM,EAAEnB,uBAAuB,EAAEmB,MAAM,IAAI,CAAC;MAC9C;MACA;MACA;MAAA;MACAtE,GAAG,EAAEyD,SAAU;MACfc,oBAAoB,EAClB,CAACrB,aAAa,GACVL,SAAS,GACTrD,qBAAQ,CAACgF,KAAK,CACZ,CACE;QACEC,WAAW,EAAE;UACX7D,QAAQ;UACRF,OAAO;UACPG;QACF;MACF,CAAC,CACF,EACD;QAAE6D,eAAe,EAAE;MAAK,CAC1B;IACL,IACA,CAACxB,aAAa;IAAK;IAClBD,QAAQ,gBAERnF,MAAA,CAAAH,OAAA,CAAAoG,aAAA,CAAC7F,0BAAA,CAAAP,OAAyB,CAACgH,QAAQ;MACjCjH,KAAK,EAAE;QACLkD,QAAQ;QACRF,OAAO;QACPG;MACF;IAAE,GACDoC,QACiC,CAExB,CACH,CAAC;EAEpB,CAAC,MAAM;IACL;IACA,IAAI;MACFD,MAAM;MACNzC,aAAa;MACb8C,KAAK;MACL;MACA5C,cAAc;MACd,GAAGV;IACL,CAAC,GAAGoB,IAAI;IAER,IAAI6B,MAAM,KAAKH,SAAS,IAAItC,aAAa,KAAKsC,SAAS,EAAE;MACvDtC,aAAa,GAAGyC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;IACtC;IACA,oBACElF,MAAA,CAAAH,OAAA,CAAAoG,aAAA,CAAC9F,YAAA,CAAAuB,QAAQ,CAACoF,IAAI,EAAAhG,QAAA;MACZyE,KAAK,EAAE,CAACA,KAAK,EAAE;QAAEO,OAAO,EAAErD,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;MAAO,CAAC,CAAE;MACnEP,GAAG,EAAEQ;IAAO,GACRT,KAAK,CACV,CAAC;EAEN;AACF,CACF,CAAC;;AAED;AACA;AACO,MAAMnC,aAAa,GAAAH,OAAA,CAAAG,aAAA,gBAAGiC,cAAK,CAACgF,aAAa,CAAChH,WAAW,CAAC;AAE7D,MAAMiH,MAAM,gBAAGjF,cAAK,CAACC,UAAU,CAAoB,CAACC,KAAK,EAAEC,GAAG,KAAK;EACjE,MAAM+E,aAAa,GAAGlF,cAAK,CAACmF,UAAU,CAACpH,aAAa,CAAC,IAAIC,WAAW;EAEpE,oBACEC,MAAA,CAAAH,OAAA,CAAAoG,aAAA,CAACgB,aAAa,EAAAnG,QAAA,KACPqG,wBAAY,GAAG,IAAAC,oCAAwB,EAACnF,KAAK,CAAC,GAAGA,KAAK;IAC3DC,GAAG,EAAEA;EAAI,EACV,CAAC;AAEN,CAAC,CAAC;AAEF8E,MAAM,CAACK,WAAW,GAAG,QAAQ;AAAC,IAAAC,QAAA,GAAA3H,OAAA,CAAAE,OAAA,GAEfmH,MAAM", "ignoreList": []}