{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNative", "_reactNativeGestureHandler", "_reactNativeReanimated", "_fabricUtils", "_RNScreensTurboModule", "_defaults", "_constraints", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "EmptyGestureHandler", "Gesture", "Fling", "enabled", "ScreenGestureDetector", "children", "gestureDetectorBridge", "goBackGesture", "screenEdgeGesture", "transitionAnimation", "customTransitionAnimation", "screensRefs", "currentScreenId", "sharedEvent", "useSharedValue", "DefaultEvent", "startingGesturePosition", "canPerformUpdates", "makeMutable", "getAnimationForTransition", "screenTransitionConfig", "stackTag", "belowTopScreenId", "topScreenId", "screenTransition", "isTransitionCanceled", "screenDimensions", "DefaultScreenDimensions", "onFinishAnimation", "screenTagToNodeWrapperUI", "IS_FABRIC", "isF<PERSON><PERSON>", "current", "stackUseEffectCallback", "stackRef", "value", "findNodeHandle", "Platform", "OS", "runOnUI", "RNScreensTurboModule", "disableSwipeBackForTopScreen", "useEffect", "undefined", "screenTagToNodeWrapper", "key", "screenRef", "screenData", "getShadowNodeWrapperAndTagFromRef", "tag", "shadowNodeWrapper", "console", "warn", "computeProgress", "event", "progress", "startingPosition", "translationX", "width", "absoluteX", "translationY", "height", "absoluteY", "Math", "abs", "progressX", "progressY", "max", "onStart", "transitionConfig", "transitionData", "startTransition", "canStartTransition", "topScreenTag", "belowTopScreenTag", "animatedRefMock", "screenSize", "measure", "Error", "finishTransition", "startScreenTransition", "onUpdate", "checkBoundaries", "updateTransition", "onEnd", "velocityFactor", "distanceX", "min", "velocityX", "distanceY", "velocityY", "requiredXDistance", "requiredYDistance", "checkIfTransitionCancelled", "finishScreenTransition", "panGesture", "Pan", "HIT_SLOP_SIZE", "ACTIVATION_DISTANCE", "activeOffsetX", "hitSlop", "left", "top", "right", "activeOffsetY", "Dimensions", "bottom", "createElement", "GestureDetector", "gesture", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["gesture-handler/ScreenGestureDetector.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,0BAAA,GAAAF,OAAA;AAMA,IAAAG,sBAAA,GAAAH,OAAA;AAQA,IAAAI,YAAA,GAAAJ,OAAA;AACA,IAAAK,qBAAA,GAAAL,OAAA;AACA,IAAAM,SAAA,GAAAN,OAAA;AACA,IAAAO,YAAA,GAAAP,OAAA;AAIuB,SAAAQ,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAV,wBAAAU,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAGvB;AACA;AACA,MAAMW,mBAAmB,GAAGC,kCAAO,CAACC,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,CAAC;AAE1D,MAAMC,qBAAqB,GAAGA,CAAC;EAC7BC,QAAQ;EACRC,qBAAqB;EACrBC,aAAa;EACbC,iBAAiB;EACjBC,mBAAmB,EAAEC,yBAAyB;EAC9CC,WAAW;EACXC;AACoB,CAAC,KAAK;EAC1B,MAAMC,WAAW,GAAG,IAAAC,qCAAc,EAACC,sBAAY,CAAC;EAChD,MAAMC,uBAAuB,GAAG,IAAAF,qCAAc,EAACC,sBAAY,CAAC;EAC5D,MAAME,iBAAiB,GAAG,IAAAC,kCAAW,EAAC,KAAK,CAAC;EAC5C,MAAMT,mBAAmB,GAAG,IAAAU,sCAAyB,EACnDZ,aAAa,EACbG,yBACF,CAAC;EACD,MAAMU,sBAAsB,GAAG,IAAAF,kCAAW,EAAC;IACzCG,QAAQ,EAAE,CAAC,CAAC;IACZC,gBAAgB,EAAE,CAAC,CAAC;IACpBC,WAAW,EAAE,CAAC,CAAC;IACfV,WAAW;IACXG,uBAAuB;IACvBQ,gBAAgB,EAAEf,mBAAmB;IACrCgB,oBAAoB,EAAE,KAAK;IAC3BlB,aAAa,EAAEA,aAAa,IAAI,YAAY;IAC5CmB,gBAAgB,EAAEC,iCAAuB;IACzCC,iBAAiB,EAAEA,CAAA,KAAM;MACvB,SAAS;IACX;EACF,CAAC,CAAC;EACF,MAAMP,QAAQ,GAAG,IAAAH,kCAAW,EAAC,CAAC,CAAC,CAAC;EAChC,MAAMW,wBAAwB,GAAG,IAAAX,kCAAW,EAAsB,CAAC,CAAC,CAAC;EACrE,MAAMY,SAAS,GAAG,IAAAC,qBAAQ,EAAC,CAAC;EAE5BzB,qBAAqB,CAAC0B,OAAO,CAACC,sBAAsB,GAAGC,QAAQ,IAAI;IACjE,IAAI,CAAC3B,aAAa,EAAE;MAClB;IACF;IACAc,QAAQ,CAACc,KAAK,GAAG,IAAAC,2BAAc,EAACF,QAAQ,CAACF,OAAc,CAAW;IAClE,IAAIK,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MACzB,IAAAC,8BAAO,EAAC,MAAM;QACZC,0CAAoB,CAACC,4BAA4B,CAACpB,QAAQ,CAACc,KAAK,CAAC;MACnE,CAAC,CAAC,CAAC,CAAC;IACN;EACF,CAAC;EAED,IAAAO,gBAAS,EAAC,MAAM;IACd,IAAI,CAACZ,SAAS,IAAI,CAACvB,aAAa,IAAII,WAAW,KAAKgC,SAAS,EAAE;MAC7D;IACF;IACA,MAAMC,sBAA+D,GAAG,CAAC,CAAC;IAC1E,KAAK,MAAMC,GAAG,IAAIlC,WAAW,CAACqB,OAAO,EAAE;MACrC,MAAMc,SAAS,GAAGnC,WAAW,CAACqB,OAAO,CAACa,GAAG,CAAC;MAC1C,MAAME,UAAU,GAAG,IAAAC,8CAAiC,EAACF,SAAS,CAACd,OAAO,CAAC;MACvE,IAAIe,UAAU,CAACE,GAAG,IAAIF,UAAU,CAACG,iBAAiB,EAAE;QAClDN,sBAAsB,CAACG,UAAU,CAACE,GAAG,CAAC,GAAGF,UAAU,CAACG,iBAAiB;MACvE,CAAC,MAAM;QACLC,OAAO,CAACC,IAAI,CAAC,4CAA4C,CAAC;MAC5D;IACF;IACAvB,wBAAwB,CAACM,KAAK,GAAGS,sBAAsB;EACzD,CAAC,EAAE,CAAChC,eAAe,EAAEL,aAAa,CAAC,CAAC;EAEpC,SAAS8C,eAAeA,CACtBC,KAAwD,EACxD;IACA,SAAS;;IACT,IAAIC,QAAQ,GAAG,CAAC;IAChB,MAAM7B,gBAAgB,GAAGN,sBAAsB,CAACe,KAAK,CAACT,gBAAgB;IACtE,MAAM8B,gBAAgB,GAAGxC,uBAAuB,CAACmB,KAAK;IACtD,IAAI5B,aAAa,KAAK,YAAY,EAAE;MAClCgD,QAAQ,GACND,KAAK,CAACG,YAAY,IACjB/B,gBAAgB,CAACgC,KAAK,GAAGF,gBAAgB,CAACG,SAAS,CAAC;IACzD,CAAC,MAAM,IAAIpD,aAAa,KAAK,WAAW,EAAE;MACxCgD,QAAQ,GAAI,CAAC,CAAC,GAAGD,KAAK,CAACG,YAAY,GAAID,gBAAgB,CAACG,SAAS;IACnE,CAAC,MAAM,IAAIpD,aAAa,KAAK,WAAW,EAAE;MACxCgD,QAAQ,GACL,CAAC,CAAC,GAAGD,KAAK,CAACM,YAAY,IACvBlC,gBAAgB,CAACmC,MAAM,GAAGL,gBAAgB,CAACM,SAAS,CAAC;IAC1D,CAAC,MAAM,IAAIvD,aAAa,KAAK,SAAS,EAAE;MACtCgD,QAAQ,GAAGD,KAAK,CAACM,YAAY,GAAGJ,gBAAgB,CAACM,SAAS;IAC5D,CAAC,MAAM,IAAIvD,aAAa,KAAK,iBAAiB,EAAE;MAC9CgD,QAAQ,GAAGQ,IAAI,CAACC,GAAG,CAACV,KAAK,CAACG,YAAY,GAAG/B,gBAAgB,CAACgC,KAAK,GAAG,CAAC,CAAC;IACtE,CAAC,MAAM,IAAInD,aAAa,KAAK,eAAe,EAAE;MAC5CgD,QAAQ,GAAGQ,IAAI,CAACC,GAAG,CAACV,KAAK,CAACM,YAAY,GAAGlC,gBAAgB,CAACmC,MAAM,GAAG,CAAC,CAAC;IACvE,CAAC,MAAM,IAAItD,aAAa,KAAK,qBAAqB,EAAE;MAClD,MAAM0D,SAAS,GAAGF,IAAI,CAACC,GAAG,CACxBV,KAAK,CAACG,YAAY,GAAG/B,gBAAgB,CAACgC,KAAK,GAAG,CAChD,CAAC;MACD,MAAMQ,SAAS,GAAGH,IAAI,CAACC,GAAG,CACxBV,KAAK,CAACM,YAAY,GAAGlC,gBAAgB,CAACmC,MAAM,GAAG,CACjD,CAAC;MACDN,QAAQ,GAAGQ,IAAI,CAACI,GAAG,CAACF,SAAS,EAAEC,SAAS,CAAC;IAC3C;IACA,OAAOX,QAAQ;EACjB;EAEA,SAASa,OAAOA,CAACd,KAAwD,EAAE;IACzE,SAAS;;IACTzC,WAAW,CAACsB,KAAK,GAAGmB,KAAK;IACzB,MAAMe,gBAAgB,GAAGjD,sBAAsB,CAACe,KAAK;IACrD,MAAMmC,cAAc,GAAG9B,0CAAoB,CAAC+B,eAAe,CAAClD,QAAQ,CAACc,KAAK,CAAC;IAC3E,IAAImC,cAAc,CAACE,kBAAkB,KAAK,KAAK,EAAE;MAC/CvD,iBAAiB,CAACkB,KAAK,GAAG,KAAK;MAC/B;IACF;IAEA,IAAIL,SAAS,EAAE;MACbuC,gBAAgB,CAAC9C,WAAW,GAC1BM,wBAAwB,CAACM,KAAK,CAACmC,cAAc,CAACG,YAAY,CAAC;MAC7DJ,gBAAgB,CAAC/C,gBAAgB,GAC/BO,wBAAwB,CAACM,KAAK,CAACmC,cAAc,CAACI,iBAAiB,CAAC;IACpE,CAAC,MAAM;MACLL,gBAAgB,CAAC9C,WAAW,GAAG+C,cAAc,CAACG,YAAY;MAC1DJ,gBAAgB,CAAC/C,gBAAgB,GAAGgD,cAAc,CAACI,iBAAiB;IACtE;IAEAL,gBAAgB,CAAChD,QAAQ,GAAGA,QAAQ,CAACc,KAAK;IAC1CnB,uBAAuB,CAACmB,KAAK,GAAGmB,KAAK;IACrC,MAAMqB,eAAe,GAAGA,CAAA,KAAM;MAC5B,OAAOvD,sBAAsB,CAACe,KAAK,CAACZ,WAAW;IACjD,CAAC;IACD,MAAMqD,UAAU,GAAG,IAAAC,8BAAO,EAACF,eAAsB,CAAC;IAClD,IAAIC,UAAU,IAAI,IAAI,EAAE;MACtB,MAAM,IAAIE,KAAK,CAAC,uCAAuC,CAAC;IAC1D;IACA,IAAIF,UAAU,IAAI,IAAI,EAAE;MACtB3D,iBAAiB,CAACkB,KAAK,GAAG,KAAK;MAC/BK,0CAAoB,CAACuC,gBAAgB,CAAC1D,QAAQ,CAACc,KAAK,EAAE,IAAI,CAAC;MAC3D;IACF;IACAkC,gBAAgB,CAAC3C,gBAAgB,GAAGkD,UAAU;IAC9C;IACA;IACA;IACA;IACA;IACA;IACA,IAAAI,4CAAqB,EAACX,gBAAuB,CAAC;IAC9CpD,iBAAiB,CAACkB,KAAK,GAAG,IAAI;EAChC;EAEA,SAAS8C,QAAQA,CAAC3B,KAAwD,EAAE;IAC1E,SAAS;;IACT,IAAI,CAACrC,iBAAiB,CAACkB,KAAK,EAAE;MAC5B;IACF;IACA,IAAA+C,4BAAe,EAAC3E,aAAa,EAAE+C,KAAK,CAAC;IACrC,MAAMC,QAAQ,GAAGF,eAAe,CAACC,KAAK,CAAC;IACvCzC,WAAW,CAACsB,KAAK,GAAGmB,KAAK;IACzB,MAAMjC,QAAQ,GAAGD,sBAAsB,CAACe,KAAK,CAACd,QAAQ;IACtDmB,0CAAoB,CAAC2C,gBAAgB,CAAC9D,QAAQ,EAAEkC,QAAQ,CAAC;EAC3D;EAEA,SAAS6B,KAAKA,CAAC9B,KAAwD,EAAE;IACvE,SAAS;;IACT,IAAI,CAACrC,iBAAiB,CAACkB,KAAK,EAAE;MAC5B;IACF;IAEA,MAAMkD,cAAc,GAAG,GAAG;IAC1B,MAAMT,UAAU,GAAGxD,sBAAsB,CAACe,KAAK,CAACT,gBAAgB;IAChE,MAAM4D,SAAS,GACbhC,KAAK,CAACG,YAAY,GAAGM,IAAI,CAACwB,GAAG,CAACjC,KAAK,CAACkC,SAAS,GAAGH,cAAc,EAAE,GAAG,CAAC;IACtE,MAAMI,SAAS,GACbnC,KAAK,CAACM,YAAY,GAAGG,IAAI,CAACwB,GAAG,CAACjC,KAAK,CAACoC,SAAS,GAAGL,cAAc,EAAE,GAAG,CAAC;IACtE,MAAMM,iBAAiB,GAAGf,UAAU,CAAClB,KAAK,GAAG,CAAC;IAC9C,MAAMkC,iBAAiB,GAAGhB,UAAU,CAACf,MAAM,GAAG,CAAC;IAC/C,MAAMpC,oBAAoB,GAAG,IAAAoE,uCAA0B,EACrDtF,aAAa,EACb+E,SAAS,EACTK,iBAAiB,EACjBF,SAAS,EACTG,iBACF,CAAC;IACD,MAAMvE,QAAQ,GAAGD,sBAAsB,CAACe,KAAK,CAACd,QAAQ;IACtDD,sBAAsB,CAACe,KAAK,CAACP,iBAAiB,GAAG,MAAM;MACrDY,0CAAoB,CAACuC,gBAAgB,CAAC1D,QAAQ,EAAEI,oBAAoB,CAAC;IACvE,CAAC;IACDL,sBAAsB,CAACe,KAAK,CAACV,oBAAoB,GAAGA,oBAAoB;IACxE;IACA;IACA;IACA;IACA;IACA;IACA,IAAAqE,6CAAsB,EAAC1E,sBAAsB,CAACe,KAAY,CAAC;EAC7D;EAEA,IAAI4D,UAAU,GAAG9F,kCAAO,CAAC+F,GAAG,CAAC,CAAC,CAC3B5B,OAAO,CAACA,OAAO,CAAC,CAChBa,QAAQ,CAACA,QAAQ,CAAC,CAClBG,KAAK,CAACA,KAAK,CAAC;EAEf,IAAI5E,iBAAiB,EAAE;IACrB,MAAMyF,aAAa,GAAG,EAAE;IACxB,MAAMC,mBAAmB,GAAG,EAAE;IAC9B,IAAI3F,aAAa,KAAK,YAAY,EAAE;MAClCwF,UAAU,GAAGA,UAAU,CACpBI,aAAa,CAACD,mBAAmB,CAAC,CAClCE,OAAO,CAAC;QAAEC,IAAI,EAAE,CAAC;QAAEC,GAAG,EAAE,CAAC;QAAE5C,KAAK,EAAEuC;MAAc,CAAC,CAAC;IACvD,CAAC,MAAM,IAAI1F,aAAa,KAAK,WAAW,EAAE;MACxCwF,UAAU,GAAGA,UAAU,CACpBI,aAAa,CAAC,CAACD,mBAAmB,CAAC,CACnCE,OAAO,CAAC;QAAEG,KAAK,EAAE,CAAC;QAAED,GAAG,EAAE,CAAC;QAAE5C,KAAK,EAAEuC;MAAc,CAAC,CAAC;IACxD,CAAC,MAAM,IAAI1F,aAAa,KAAK,WAAW,EAAE;MACxCwF,UAAU,GAAGA,UAAU,CACpBS,aAAa,CAACN,mBAAmB,CAAC,CAClCE,OAAO,CAAC;QAAEE,GAAG,EAAE,CAAC;QAAEzC,MAAM,EAAE4C,uBAAU,CAACrH,GAAG,CAAC,QAAQ,CAAC,CAACyE,MAAM,GAAG;MAAI,CAAC,CAAC;MACrE;IACF,CAAC,MAAM,IAAItD,aAAa,KAAK,SAAS,EAAE;MACtCwF,UAAU,GAAGA,UAAU,CACpBS,aAAa,CAAC,CAACN,mBAAmB,CAAC,CACnCE,OAAO,CAAC;QAAEM,MAAM,EAAE,CAAC;QAAE7C,MAAM,EAAEoC;MAAc,CAAC,CAAC;IAClD;EACF;EACA,oBACE/H,MAAA,CAAAgB,OAAA,CAAAyH,aAAA,CAACrI,0BAAA,CAAAsI,eAAe;IAACC,OAAO,EAAEtG,aAAa,GAAGwF,UAAU,GAAG/F;EAAoB,GACxEK,QACc,CAAC;AAEtB,CAAC;AAAC,IAAAyG,QAAA,GAAAC,OAAA,CAAA7H,OAAA,GAEakB,qBAAqB", "ignoreList": []}