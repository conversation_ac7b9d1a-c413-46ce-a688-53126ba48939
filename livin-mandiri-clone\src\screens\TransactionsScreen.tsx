import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Header, Card, Input, Loading } from '../components';
import { Colors, Typography, Spacing, BorderRadius, Layout } from '../constants';

interface Transaction {
  id: string;
  title: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
  category: string;
  date: string;
  time: string;
  icon: keyof typeof Ionicons.glyphMap;
  status: 'completed' | 'pending' | 'failed';
}

interface TransactionsScreenProps {
  navigation: any;
}

export const TransactionsScreen: React.FC<TransactionsScreenProps> = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'income' | 'expense'>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const [transactions] = useState<Transaction[]>([
    {
      id: '1',
      title: 'Salary Payment',
      description: 'Monthly salary from PT. ABC',
      amount: 12500000,
      type: 'income',
      category: 'Salary',
      date: '2024-07-18',
      time: '09:00',
      icon: 'briefcase',
      status: 'completed',
    },
    {
      id: '2',
      title: 'Restaurant Payment',
      description: 'Dinner at Sushi Tei',
      amount: -125000,
      type: 'expense',
      category: 'Food & Dining',
      date: '2024-07-18',
      time: '19:30',
      icon: 'restaurant',
      status: 'completed',
    },
    {
      id: '3',
      title: 'Transfer to John',
      description: 'Split bill payment',
      amount: -250000,
      type: 'expense',
      category: 'Transfer',
      date: '2024-07-17',
      time: '15:45',
      icon: 'send',
      status: 'completed',
    },
    {
      id: '4',
      title: 'Online Shopping',
      description: 'Tokopedia purchase',
      amount: -450000,
      type: 'expense',
      category: 'Shopping',
      date: '2024-07-17',
      time: '10:20',
      icon: 'bag',
      status: 'pending',
    },
    {
      id: '5',
      title: 'Freelance Payment',
      description: 'Web development project',
      amount: 3500000,
      type: 'income',
      category: 'Freelance',
      date: '2024-07-16',
      time: '14:00',
      icon: 'laptop',
      status: 'completed',
    },
  ]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(Math.abs(amount));
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('id-ID', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
      });
    }
  };

  const getStatusColor = (status: Transaction['status']) => {
    switch (status) {
      case 'completed':
        return Colors.status.success;
      case 'pending':
        return Colors.status.warning;
      case 'failed':
        return Colors.status.error;
      default:
        return Colors.text.secondary;
    }
  };

  const filteredTransactions = transactions.filter((transaction) => {
    const matchesSearch = transaction.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         transaction.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = selectedFilter === 'all' || transaction.type === selectedFilter;
    return matchesSearch && matchesFilter;
  });

  const onRefresh = async () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const handleTransactionPress = (transaction: Transaction) => {
    navigation.navigate('TransactionDetail', { transaction });
  };

  const renderFilterButton = (filter: 'all' | 'income' | 'expense', title: string) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        selectedFilter === filter && styles.activeFilterButton,
      ]}
      onPress={() => setSelectedFilter(filter)}
    >
      <Text
        style={[
          styles.filterButtonText,
          selectedFilter === filter && styles.activeFilterButtonText,
        ]}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );

  const renderTransactionItem = ({ item }: { item: Transaction }) => (
    <TouchableOpacity onPress={() => handleTransactionPress(item)}>
      <Card padding="md" margin="xs" style={styles.transactionCard}>
        <View style={styles.transactionContent}>
          <View style={styles.transactionIcon}>
            <Ionicons
              name={item.icon}
              size={Layout.iconSize.md}
              color={Colors.primary.main}
            />
          </View>

          <View style={styles.transactionDetails}>
            <View style={styles.transactionHeader}>
              <Text style={styles.transactionTitle}>{item.title}</Text>
              <Text
                style={[
                  styles.transactionAmount,
                  item.type === 'income' ? styles.incomeAmount : styles.expenseAmount,
                ]}
              >
                {item.type === 'income' ? '+' : '-'}{formatCurrency(item.amount)}
              </Text>
            </View>

            <View style={styles.transactionMeta}>
              <Text style={styles.transactionDescription}>{item.description}</Text>
              <View style={styles.transactionStatus}>
                <View
                  style={[
                    styles.statusDot,
                    { backgroundColor: getStatusColor(item.status) },
                  ]}
                />
                <Text style={styles.statusText}>{item.status}</Text>
              </View>
            </View>

            <View style={styles.transactionFooter}>
              <Text style={styles.transactionDate}>
                {formatDate(item.date)} • {item.time}
              </Text>
              <Text style={styles.transactionCategory}>{item.category}</Text>
            </View>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );

  const renderHeader = () => (
    <View style={styles.headerContent}>
      <Input
        placeholder="Search transactions..."
        value={searchQuery}
        onChangeText={setSearchQuery}
        leftIcon="search"
        style={styles.searchInput}
      />

      <View style={styles.filterContainer}>
        {renderFilterButton('all', 'All')}
        {renderFilterButton('income', 'Income')}
        {renderFilterButton('expense', 'Expense')}
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <Header
        title="Transactions"
        showBackButton
        onLeftPress={() => navigation.goBack()}
        rightIcon="filter"
        onRightPress={() => console.log('Filter')}
      />

      <FlatList
        data={filteredTransactions}
        renderItem={renderTransactionItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />

      <Loading visible={isLoading} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },

  headerContent: {
    padding: Spacing.md,
    backgroundColor: Colors.background.primary,
  },

  searchInput: {
    marginBottom: Spacing.md,
  },

  filterContainer: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },

  filterButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.neutral.gray100,
    borderWidth: 1,
    borderColor: Colors.border.light,
  },

  activeFilterButton: {
    backgroundColor: Colors.primary.main,
    borderColor: Colors.primary.main,
  },

  filterButtonText: {
    ...Typography.bodySmall,
    color: Colors.text.secondary,
    fontWeight: '500',
  },

  activeFilterButtonText: {
    color: Colors.text.inverse,
  },

  listContent: {
    paddingBottom: Spacing.xl,
  },

  transactionCard: {
    marginHorizontal: Spacing.md,
  },

  transactionContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },

  transactionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.neutral.gray50,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },

  transactionDetails: {
    flex: 1,
  },

  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.xs,
  },

  transactionTitle: {
    ...Typography.body,
    color: Colors.text.primary,
    fontWeight: '600',
    flex: 1,
    marginRight: Spacing.sm,
  },

  transactionAmount: {
    ...Typography.body,
    fontWeight: 'bold',
  },

  incomeAmount: {
    color: Colors.status.success,
  },

  expenseAmount: {
    color: Colors.status.error,
  },

  transactionMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },

  transactionDescription: {
    ...Typography.bodySmall,
    color: Colors.text.secondary,
    flex: 1,
    marginRight: Spacing.sm,
  },

  transactionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  statusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: Spacing.xs,
  },

  statusText: {
    ...Typography.caption,
    color: Colors.text.tertiary,
    textTransform: 'capitalize',
  },

  transactionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  transactionDate: {
    ...Typography.caption,
    color: Colors.text.tertiary,
  },

  transactionCategory: {
    ...Typography.caption,
    color: Colors.primary.main,
    fontWeight: '500',
  },
});
