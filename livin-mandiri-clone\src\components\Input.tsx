import React, { useState } from 'react';
import {
  View,
  TextInput,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInputProps,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors, Typography, Spacing, BorderRadius, Layout } from '../constants';

export interface InputProps extends Omit<TextInputProps, 'style'> {
  label?: string;
  error?: string;
  hint?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onRightIconPress?: () => void;
  variant?: 'default' | 'outline' | 'filled';
  size?: 'small' | 'medium' | 'large';
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
  hintStyle?: TextStyle;
  disabled?: boolean;
  required?: boolean;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  hint,
  leftIcon,
  rightIcon,
  onRightIconPress,
  variant = 'outline',
  size = 'medium',
  containerStyle,
  inputStyle,
  labelStyle,
  errorStyle,
  hintStyle,
  disabled = false,
  required = false,
  secureTextEntry,
  ...textInputProps
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isSecure, setIsSecure] = useState(secureTextEntry);

  const hasError = !!error;
  const isPasswordField = secureTextEntry;

  const containerStyles = [
    styles.container,
    containerStyle,
  ];

  const inputContainerStyles = [
    styles.inputContainer,
    styles[variant],
    styles[size],
    isFocused && styles.focused,
    hasError && styles.error,
    disabled && styles.disabled,
  ];

  const inputStyles = [
    styles.input,
    styles[`${size}Input`],
    leftIcon && styles.inputWithLeftIcon,
    (rightIcon || isPasswordField) && styles.inputWithRightIcon,
    disabled && styles.disabledInput,
    inputStyle,
  ];

  const labelStyles = [
    styles.label,
    hasError && styles.errorLabel,
    disabled && styles.disabledLabel,
    labelStyle,
  ];

  const handleToggleSecure = () => {
    setIsSecure(!isSecure);
  };

  const renderRightIcon = () => {
    if (isPasswordField) {
      return (
        <TouchableOpacity onPress={handleToggleSecure} style={styles.iconButton}>
          <Ionicons
            name={isSecure ? 'eye-off' : 'eye'}
            size={Layout.iconSize.md}
            color={Colors.neutral.gray500}
          />
        </TouchableOpacity>
      );
    }

    if (rightIcon) {
      return (
        <TouchableOpacity onPress={onRightIconPress} style={styles.iconButton}>
          <Ionicons
            name={rightIcon}
            size={Layout.iconSize.md}
            color={Colors.neutral.gray500}
          />
        </TouchableOpacity>
      );
    }

    return null;
  };

  return (
    <View style={containerStyles}>
      {label && (
        <Text style={labelStyles}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      
      <View style={inputContainerStyles}>
        {leftIcon && (
          <View style={styles.leftIconContainer}>
            <Ionicons
              name={leftIcon}
              size={Layout.iconSize.md}
              color={Colors.neutral.gray500}
            />
          </View>
        )}
        
        <TextInput
          {...textInputProps}
          style={inputStyles}
          secureTextEntry={isSecure}
          onFocus={(e) => {
            setIsFocused(true);
            textInputProps.onFocus?.(e);
          }}
          onBlur={(e) => {
            setIsFocused(false);
            textInputProps.onBlur?.(e);
          }}
          editable={!disabled}
          placeholderTextColor={Colors.text.placeholder}
        />
        
        {renderRightIcon()}
      </View>
      
      {error && (
        <Text style={[styles.helperText, styles.errorText, errorStyle]}>
          {error}
        </Text>
      )}
      
      {hint && !error && (
        <Text style={[styles.helperText, hintStyle]}>
          {hint}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.md,
  },

  label: {
    ...Typography.bodySmall,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs,
    fontWeight: '500',
  },

  required: {
    color: Colors.status.error,
  },

  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.background.card,
  },

  // Variants
  default: {
    backgroundColor: Colors.neutral.gray50,
  },

  outline: {
    borderWidth: 1,
    borderColor: Colors.border.light,
    backgroundColor: Colors.background.card,
  },

  filled: {
    backgroundColor: Colors.neutral.gray100,
  },

  // Sizes
  small: {
    height: 40,
    paddingHorizontal: Spacing.sm,
  },

  medium: {
    height: Layout.inputHeight,
    paddingHorizontal: Layout.inputPadding,
  },

  large: {
    height: 56,
    paddingHorizontal: Spacing.lg,
  },

  // States
  focused: {
    borderColor: Colors.primary.main,
    borderWidth: 2,
  },

  error: {
    borderColor: Colors.status.error,
    borderWidth: 1,
  },

  disabled: {
    backgroundColor: Colors.neutral.gray100,
    opacity: 0.6,
  },

  input: {
    flex: 1,
    ...Typography.body,
    color: Colors.text.primary,
    padding: 0,
  },

  smallInput: {
    ...Typography.bodySmall,
  },

  mediumInput: {
    ...Typography.body,
  },

  largeInput: {
    ...Typography.bodyLarge,
  },

  inputWithLeftIcon: {
    marginLeft: Spacing.sm,
  },

  inputWithRightIcon: {
    marginRight: Spacing.sm,
  },

  disabledInput: {
    color: Colors.neutral.gray500,
  },

  leftIconContainer: {
    marginLeft: Spacing.xs,
  },

  iconButton: {
    padding: Spacing.xs,
    marginRight: Spacing.xs,
  },

  helperText: {
    ...Typography.caption,
    marginTop: Spacing.xs,
    marginLeft: Spacing.xs,
  },

  errorText: {
    color: Colors.status.error,
  },

  errorLabel: {
    color: Colors.status.error,
  },

  disabledLabel: {
    color: Colors.neutral.gray500,
  },
});
