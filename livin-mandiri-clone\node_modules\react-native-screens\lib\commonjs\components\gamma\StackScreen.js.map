{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_StackScreenNativeComponent", "e", "__esModule", "default", "StackScreenLifecycleState", "exports", "INITIAL", "DETACHED", "ATTACHED", "StackScreen", "children", "maxLifecycleState", "<PERSON><PERSON><PERSON>", "onWillAppear", "onWillDisappear", "onDidAppear", "onDidDisappear", "onPop", "handleOnDidDisappear", "React", "useCallback", "createElement", "style", "StyleSheet", "absoluteFill", "_default"], "sourceRoot": "../../../../src", "sources": ["components/gamma/StackScreen.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,2BAAA,GAAAH,sBAAA,CAAAC,OAAA;AAAuF,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAIhF,MAAMG,yBAAyB,GAAAC,OAAA,CAAAD,yBAAA,GAAG;EACvCE,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE;AACZ,CAAU;AAaV;AACA;AACA;AACA,SAASC,WAAWA,CAAC;EACnBC,QAAQ;EACR;EACAC,iBAAiB;EACjBC,SAAS;EACT;EACAC,YAAY;EACZC,eAAe;EACfC,WAAW;EACXC,cAAc;EACd;EACAC;AACgB,CAAC,EAAE;EACnB,MAAMC,oBAAoB,GAAGC,cAAK,CAACC,WAAW,CAC3CnB,CAA8C,IAAK;IAClDe,cAAc,GAAGf,CAAC,CAAC;IACnBgB,KAAK,GAAGL,SAAS,CAAC;EACpB,CAAC,EACD,CAACI,cAAc,EAAEC,KAAK,EAAEL,SAAS,CACnC,CAAC;EAED,oBACEhB,MAAA,CAAAO,OAAA,CAAAkB,aAAA,CAACrB,2BAAA,CAAAG,OAA0B;IACzBmB,KAAK,EAAEC,uBAAU,CAACC;IAClB;IAAA;IACAb,iBAAiB,EAAEA,iBAAkB;IACrCC,SAAS,EAAEA;IACX;IAAA;IACAC,YAAY,EAAEA,YAAa;IAC3BE,WAAW,EAAEA,WAAY;IACzBD,eAAe,EAAEA,eAAgB;IACjCE,cAAc,EAAEE;EAAqB,GACpCR,QACyB,CAAC;AAEjC;AAAC,IAAAe,QAAA,GAAApB,OAAA,CAAAF,OAAA,GAEcM,WAAW", "ignoreList": []}