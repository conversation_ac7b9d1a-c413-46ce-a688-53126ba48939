import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Header, Card, Input, Button, Loading } from '../components';
import { Colors, Typography, Spacing, BorderRadius, Layout } from '../constants';

interface Contact {
  id: string;
  name: string;
  accountNumber: string;
  bank: string;
  avatar?: string;
}

interface TransferScreenProps {
  navigation: any;
}

export const TransferScreen: React.FC<TransferScreenProps> = ({ navigation }) => {
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [accountNumber, setAccountNumber] = useState('');
  const [amount, setAmount] = useState('');
  const [note, setNote] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{
    accountNumber?: string;
    amount?: string;
  }>({});

  const [recentContacts] = useState<Contact[]>([
    {
      id: '1',
      name: '<PERSON>',
      accountNumber: '**********',
      bank: 'Bank Mandiri',
    },
    {
      id: '2',
      name: 'Jane Smith',
      accountNumber: '**********',
      bank: 'Bank BCA',
    },
    {
      id: '3',
      name: 'Bob Wilson',
      accountNumber: '**********',
      bank: 'Bank BNI',
    },
  ]);

  const formatCurrency = (value: string) => {
    const numericValue = value.replace(/[^0-9]/g, '');
    if (!numericValue) return '';
    
    return new Intl.NumberFormat('id-ID').format(parseInt(numericValue));
  };

  const validateForm = () => {
    const newErrors: { accountNumber?: string; amount?: string } = {};

    if (!selectedContact && !accountNumber.trim()) {
      newErrors.accountNumber = 'Please select a contact or enter account number';
    } else if (!selectedContact && accountNumber.length < 10) {
      newErrors.accountNumber = 'Account number must be at least 10 digits';
    }

    if (!amount.trim()) {
      newErrors.amount = 'Amount is required';
    } else {
      const numericAmount = parseInt(amount.replace(/[^0-9]/g, ''));
      if (numericAmount < 10000) {
        newErrors.amount = 'Minimum transfer amount is Rp 10,000';
      } else if (numericAmount > ********) {
        newErrors.amount = 'Maximum transfer amount is Rp 50,000,000';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleContactSelect = (contact: Contact) => {
    setSelectedContact(contact);
    setAccountNumber(contact.accountNumber);
    setErrors({ ...errors, accountNumber: undefined });
  };

  const handleAccountNumberChange = (value: string) => {
    setAccountNumber(value);
    setSelectedContact(null);
    if (errors.accountNumber) {
      setErrors({ ...errors, accountNumber: undefined });
    }
  };

  const handleAmountChange = (value: string) => {
    const formatted = formatCurrency(value);
    setAmount(formatted);
    if (errors.amount) {
      setErrors({ ...errors, amount: undefined });
    }
  };

  const handleTransfer = async () => {
    if (!validateForm()) return;

    const transferData = {
      recipient: selectedContact || {
        name: 'Unknown',
        accountNumber,
        bank: 'Unknown Bank',
      },
      amount: parseInt(amount.replace(/[^0-9]/g, '')),
      note,
    };

    navigation.navigate('TransferConfirmation', { transferData });
  };

  const handleSaveContact = () => {
    if (!accountNumber.trim()) {
      Alert.alert('Error', 'Please enter an account number first');
      return;
    }

    Alert.alert(
      'Save Contact',
      'Do you want to save this account as a contact?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Save',
          onPress: () => {
            // Implement save contact functionality
            Alert.alert('Success', 'Contact saved successfully!');
          },
        },
      ]
    );
  };

  const renderRecentContacts = () => (
    <Card padding="lg" style={styles.contactsCard}>
      <Text style={styles.sectionTitle}>Recent Contacts</Text>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.contactsList}
      >
        {recentContacts.map((contact) => (
          <TouchableOpacity
            key={contact.id}
            style={[
              styles.contactItem,
              selectedContact?.id === contact.id && styles.selectedContact,
            ]}
            onPress={() => handleContactSelect(contact)}
          >
            <View style={styles.contactAvatar}>
              <Text style={styles.contactInitial}>
                {contact.name.charAt(0)}
              </Text>
            </View>
            <Text style={styles.contactName}>{contact.name}</Text>
            <Text style={styles.contactBank}>{contact.bank}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </Card>
  );

  const renderTransferForm = () => (
    <Card padding="lg" style={styles.formCard}>
      <Text style={styles.sectionTitle}>Transfer Details</Text>

      <View style={styles.accountSection}>
        <Input
          label="Account Number"
          placeholder="Enter account number"
          value={accountNumber}
          onChangeText={handleAccountNumberChange}
          error={errors.accountNumber}
          keyboardType="numeric"
          maxLength={20}
          rightIcon="bookmark"
          onRightIconPress={handleSaveContact}
        />

        {selectedContact && (
          <View style={styles.selectedContactInfo}>
            <Ionicons
              name="checkmark-circle"
              size={Layout.iconSize.sm}
              color={Colors.status.success}
            />
            <Text style={styles.selectedContactText}>
              {selectedContact.name} - {selectedContact.bank}
            </Text>
          </View>
        )}
      </View>

      <Input
        label="Amount"
        placeholder="0"
        value={amount}
        onChangeText={handleAmountChange}
        error={errors.amount}
        keyboardType="numeric"
        leftIcon="cash"
      />

      <Input
        label="Note (Optional)"
        placeholder="Add a note for this transfer"
        value={note}
        onChangeText={setNote}
        multiline
        numberOfLines={3}
        style={styles.noteInput}
      />

      <Button
        title="Continue"
        onPress={handleTransfer}
        variant="gradient"
        size="large"
        fullWidth
        disabled={!accountNumber || !amount}
        style={styles.continueButton}
      />
    </Card>
  );

  return (
    <View style={styles.container}>
      <Header
        title="Transfer"
        showBackButton
        onLeftPress={() => navigation.goBack()}
        rightIcon="help-circle"
        onRightPress={() => console.log('Help')}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {renderRecentContacts()}
        {renderTransferForm()}
      </ScrollView>

      <Loading visible={isLoading} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },

  scrollView: {
    flex: 1,
  },

  scrollContent: {
    paddingBottom: Spacing.xl,
  },

  contactsCard: {
    marginHorizontal: Spacing.md,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },

  sectionTitle: {
    ...Typography.h6,
    color: Colors.text.primary,
    fontWeight: 'bold',
    marginBottom: Spacing.md,
  },

  contactsList: {
    paddingRight: Spacing.md,
  },

  contactItem: {
    alignItems: 'center',
    marginRight: Spacing.md,
    padding: Spacing.sm,
    borderRadius: BorderRadius.md,
    minWidth: 80,
  },

  selectedContact: {
    backgroundColor: Colors.primary.light,
  },

  contactAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.primary.main,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.sm,
  },

  contactInitial: {
    ...Typography.body,
    color: Colors.text.inverse,
    fontWeight: 'bold',
  },

  contactName: {
    ...Typography.caption,
    color: Colors.text.primary,
    fontWeight: '500',
    textAlign: 'center',
    marginBottom: 2,
  },

  contactBank: {
    ...Typography.caption,
    color: Colors.text.secondary,
    textAlign: 'center',
    fontSize: 10,
  },

  formCard: {
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.md,
  },

  accountSection: {
    marginBottom: Spacing.md,
  },

  selectedContactInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Spacing.xs,
    padding: Spacing.sm,
    backgroundColor: Colors.status.success + '10',
    borderRadius: BorderRadius.sm,
  },

  selectedContactText: {
    ...Typography.bodySmall,
    color: Colors.status.success,
    marginLeft: Spacing.xs,
    fontWeight: '500',
  },

  noteInput: {
    height: 80,
  },

  continueButton: {
    marginTop: Spacing.lg,
  },
});
