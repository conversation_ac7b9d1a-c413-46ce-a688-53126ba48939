import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors, Typography, Spacing, BorderRadius, Layout, Shadow } from '../constants';

export interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'gradient';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  fullWidth = false,
  icon,
  iconPosition = 'left',
  style,
  textStyle,
}) => {
  const buttonStyle = [
    styles.base,
    styles[size],
    styles[variant],
    fullWidth && styles.fullWidth,
    disabled && styles.disabled,
    style,
  ];

  const textStyles = [
    styles.text,
    styles[`${size}Text`],
    styles[`${variant}Text`],
    disabled && styles.disabledText,
    textStyle,
  ];

  const handlePress = () => {
    if (!disabled && !loading) {
      onPress();
    }
  };

  const renderContent = () => (
    <>
      {icon && iconPosition === 'left' && (
        <>{icon}</>
      )}
      {loading ? (
        <ActivityIndicator
          size="small"
          color={variant === 'primary' || variant === 'gradient' ? Colors.neutral.white : Colors.primary.main}
        />
      ) : (
        <Text style={textStyles}>{title}</Text>
      )}
      {icon && iconPosition === 'right' && (
        <>{icon}</>
      )}
    </>
  );

  if (variant === 'gradient') {
    return (
      <TouchableOpacity
        onPress={handlePress}
        disabled={disabled || loading}
        style={[buttonStyle, { padding: 0 }]}
        activeOpacity={0.8}
      >
        <LinearGradient
          colors={disabled ? [Colors.neutral.gray300, Colors.neutral.gray400] : Colors.gradient.primary}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[styles.gradientContainer, styles[size]]}
        >
          {renderContent()}
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      onPress={handlePress}
      disabled={disabled || loading}
      style={buttonStyle}
      activeOpacity={0.8}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  base: {
    borderRadius: BorderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    ...Shadow.small,
  },

  // Sizes
  small: {
    height: Layout.buttonHeight.small,
    paddingHorizontal: Layout.buttonPadding.small,
    minWidth: 80,
  },

  medium: {
    height: Layout.buttonHeight.medium,
    paddingHorizontal: Layout.buttonPadding.medium,
    minWidth: 100,
  },

  large: {
    height: Layout.buttonHeight.large,
    paddingHorizontal: Layout.buttonPadding.large,
    minWidth: 120,
  },

  // Variants
  primary: {
    backgroundColor: Colors.primary.main,
  },

  secondary: {
    backgroundColor: Colors.secondary.main,
  },

  outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.primary.main,
  },

  ghost: {
    backgroundColor: 'transparent',
  },

  gradient: {
    backgroundColor: 'transparent',
  },

  // States
  disabled: {
    backgroundColor: Colors.neutral.gray300,
    ...Shadow.none,
  },

  fullWidth: {
    width: '100%',
  },

  // Text styles
  text: {
    textAlign: 'center',
    ...Typography.button,
  },

  smallText: {
    ...Typography.buttonSmall,
  },

  mediumText: {
    ...Typography.button,
  },

  largeText: {
    ...Typography.buttonLarge,
  },

  primaryText: {
    color: Colors.text.inverse,
  },

  secondaryText: {
    color: Colors.text.inverse,
  },

  outlineText: {
    color: Colors.primary.main,
  },

  ghostText: {
    color: Colors.primary.main,
  },

  gradientText: {
    color: Colors.text.inverse,
  },

  disabledText: {
    color: Colors.neutral.gray500,
  },

  gradientContainer: {
    borderRadius: BorderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    width: '100%',
  },
});
