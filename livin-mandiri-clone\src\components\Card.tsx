import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors, Spacing, BorderRadius, Shadow } from '../constants';

export interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'gradient' | 'outline';
  padding?: keyof typeof Spacing;
  margin?: keyof typeof Spacing;
  onPress?: () => void;
  style?: ViewStyle;
  gradientColors?: string[];
  disabled?: boolean;
}

export const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = 'md',
  margin = 'sm',
  onPress,
  style,
  gradientColors = Colors.gradient.light,
  disabled = false,
}) => {
  const cardStyle = [
    styles.base,
    styles[variant],
    {
      padding: Spacing[padding],
      margin: Spacing[margin],
    },
    disabled && styles.disabled,
    style,
  ];

  const CardContent = () => (
    <View style={cardStyle}>
      {children}
    </View>
  );

  const GradientCard = () => (
    <LinearGradient
      colors={gradientColors}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={[
        styles.base,
        {
          padding: Spacing[padding],
          margin: Spacing[margin],
        },
        disabled && styles.disabled,
        style,
      ]}
    >
      {children}
    </LinearGradient>
  );

  if (variant === 'gradient') {
    if (onPress && !disabled) {
      return (
        <TouchableOpacity
          onPress={onPress}
          disabled={disabled}
          activeOpacity={0.8}
          style={{ margin: Spacing[margin] }}
        >
          <LinearGradient
            colors={gradientColors}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={[
              styles.base,
              {
                padding: Spacing[padding],
                margin: 0,
              },
              disabled && styles.disabled,
              style,
            ]}
          >
            {children}
          </LinearGradient>
        </TouchableOpacity>
      );
    }
    return <GradientCard />;
  }

  if (onPress && !disabled) {
    return (
      <TouchableOpacity
        onPress={onPress}
        disabled={disabled}
        activeOpacity={0.8}
        style={{ margin: Spacing[margin] }}
      >
        <View style={[cardStyle, { margin: 0 }]}>
          {children}
        </View>
      </TouchableOpacity>
    );
  }

  return <CardContent />;
};

const styles = StyleSheet.create({
  base: {
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.background.card,
  },

  default: {
    backgroundColor: Colors.background.card,
    ...Shadow.small,
  },

  elevated: {
    backgroundColor: Colors.background.card,
    ...Shadow.medium,
  },

  gradient: {
    // Gradient styles are applied via LinearGradient component
  },

  outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.border.light,
    ...Shadow.none,
  },

  disabled: {
    opacity: 0.6,
  },
});
