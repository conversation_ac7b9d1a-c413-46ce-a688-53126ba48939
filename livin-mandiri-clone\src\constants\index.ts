// Export all design system constants

export * from './colors';
export * from './typography';
export * from './spacing';

// App-specific constants
export const APP_CONFIG = {
  name: 'Livin\' by <PERSON><PERSON><PERSON>lone',
  version: '1.0.0',
  description: 'A design replica of <PERSON><PERSON>\' by Mandiri mobile banking app',
} as const;

// Navigation constants
export const ROUTES = {
  // Auth Stack
  LOGIN: 'Login',
  PIN_SETUP: 'PinSetup',
  BIOMETRIC_SETUP: 'BiometricSetup',
  
  // Main Tab Stack
  HOME: 'Home',
  TRANSACTIONS: 'Transactions',
  TRANSFER: 'Transfer',
  PROFILE: 'Profile',
  
  // Modal Screens
  TRANSACTION_DETAIL: 'TransactionDetail',
  TRANSFER_CONFIRMATION: 'TransferConfirmation',
  CONTACT_PICKER: 'ContactPicker',
  ACCOUNT_SWITCHER: 'AccountSwitcher',
  SPLIT_BILL: 'SplitBill',
  CURRENCY_EXCHANGE: 'CurrencyExchange',
} as const;

// Animation constants
export const ANIMATION = {
  duration: {
    fast: 200,
    normal: 300,
    slow: 500,
  },
  
  easing: {
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
} as const;

// API endpoints (mock for this demo)
export const API_ENDPOINTS = {
  BASE_URL: 'https://api.livin-mandiri-clone.com',
  AUTH: '/auth',
  ACCOUNTS: '/accounts',
  TRANSACTIONS: '/transactions',
  TRANSFERS: '/transfers',
  CONTACTS: '/contacts',
} as const;
